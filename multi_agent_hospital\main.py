"""
Multi-Agent Hospital System - Main Application

Entry point for the hospital management system with multi-agent coordination
and agentic chatbot interface.
"""

import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import structlog
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config.settings import settings
from core.agent_framework.agent_registry import AgentRegistry
from core.agent_framework.communication_bus import AgentCommunicationBus
from core.coordinator.central_coordinator import CentralCoordinatorAgent
from chatbot.agentic_chatbot import AgenticHospitalChatBot
from agents.bed_management.bed_agent import BedManagementAgent
from agents.patient_care.patient_agent import PatientCareAgent
from api.endpoints import chat, agents, health

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class HospitalSystem:
    """Main hospital system orchestrator"""
    
    def __init__(self):
        self.communication_bus: AgentCommunicationBus = None
        self.agent_registry: AgentRegistry = None
        self.coordinator: CentralCoordinatorAgent = None
        self.chatbot: AgenticHospitalChatBot = None
        self.agents: Dict[str, Any] = {}
        self.running = False
    
    async def initialize(self):
        """Initialize the hospital system"""
        try:
            logger.info("Initializing Multi-Agent Hospital System")
            
            # Initialize communication bus
            self.communication_bus = AgentCommunicationBus()
            await self.communication_bus.initialize()
            
            # Initialize agent registry
            self.agent_registry = AgentRegistry(self.communication_bus)
            await self.agent_registry.start()
            
            # Initialize and register agents
            await self._initialize_agents()
            
            # Initialize central coordinator
            self.coordinator = CentralCoordinatorAgent(
                self.agent_registry,
                self.communication_bus
            )
            await self.agent_registry.register_agent(self.coordinator)
            
            # Initialize chatbot
            self.chatbot = AgenticHospitalChatBot(
                self.coordinator,
                self.agent_registry,
                self.communication_bus
            )
            
            self.running = True
            logger.info("Hospital system initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize hospital system", error=str(e))
            raise
    
    async def _initialize_agents(self):
        """Initialize and register all hospital agents"""
        
        # Bed Management Agent
        bed_agent = BedManagementAgent()
        await self.agent_registry.register_agent(bed_agent)
        self.agents["bed_management"] = bed_agent
        
        # Patient Care Agent
        patient_agent = PatientCareAgent()
        await self.agent_registry.register_agent(patient_agent)
        self.agents["patient_care"] = patient_agent
        
        # TODO: Add other agents (Staff, Emergency, Resource, Pharmacy, Lab, Transport)
        # For now, we'll start with these two core agents
        
        logger.info(f"Initialized {len(self.agents)} agents")
    
    async def shutdown(self):
        """Shutdown the hospital system"""
        try:
            logger.info("Shutting down hospital system")
            
            self.running = False
            
            # Stop agent registry
            if self.agent_registry:
                await self.agent_registry.stop()
            
            # Shutdown communication bus
            if self.communication_bus:
                await self.communication_bus.shutdown()
            
            logger.info("Hospital system shutdown complete")
            
        except Exception as e:
            logger.error("Error during shutdown", error=str(e))


# Global system instance
hospital_system = HospitalSystem()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    await hospital_system.initialize()
    
    # Make system components available to the app
    app.state.hospital_system = hospital_system
    app.state.chatbot = hospital_system.chatbot
    app.state.coordinator = hospital_system.coordinator
    app.state.agent_registry = hospital_system.agent_registry
    
    yield
    
    # Shutdown
    await hospital_system.shutdown()


# Create FastAPI application
app = FastAPI(
    title="Multi-Agent Hospital System",
    description="Intelligent hospital management system with multi-agent coordination and agentic chatbot",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Include API routers
app.include_router(chat.router, prefix="/api/chat", tags=["Chat"])
app.include_router(agents.router, prefix="/api/agents", tags=["Agents"])
app.include_router(health.router, prefix="/api/health", tags=["Health"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Multi-Agent Hospital System API",
        "version": "1.0.0",
        "status": "running" if hospital_system.running else "stopped",
        "docs": "/docs",
        "health": "/api/health"
    }


@app.get("/api/system/status")
async def system_status():
    """Get system status"""
    if not hospital_system.running:
        raise HTTPException(status_code=503, detail="System not running")
    
    try:
        agent_stats = hospital_system.agent_registry.get_registry_stats()
        active_sessions = hospital_system.chatbot.get_active_sessions_count()
        
        return {
            "status": "running",
            "agents": agent_stats,
            "active_chat_sessions": active_sessions,
            "system_uptime": "N/A",  # Would calculate actual uptime
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error("Error getting system status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system status")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc)
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred. Please try again later.",
            "path": request.url.path
        }
    )


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating shutdown")
        asyncio.create_task(hospital_system.shutdown())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main entry point"""
    setup_signal_handlers()
    
    logger.info(
        "Starting Multi-Agent Hospital System",
        host=settings.api_host,
        port=settings.api_port,
        debug=settings.debug
    )
    
    config = uvicorn.Config(
        app,
        host=settings.api_host,
        port=settings.api_port,
        log_level=settings.log_level.lower(),
        reload=settings.debug,
        workers=1  # Single worker for now due to shared state
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main())
