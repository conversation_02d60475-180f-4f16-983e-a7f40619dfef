"""
Agent Registry

Manages registration, discovery, and lifecycle of all hospital agents.
Provides a centralized way to find and interact with agents.
"""

import asyncio
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

import structlog
from .base_agent import BaseHospitalAgent, AgentCapability, AgentStatus
from .communication_bus import AgentCommunicationBus

logger = structlog.get_logger(__name__)


@dataclass
class AgentRegistration:
    """Agent registration information"""
    agent_id: str
    name: str
    description: str
    capabilities: List[AgentCapability]
    status: AgentStatus
    registered_at: datetime
    last_heartbeat: datetime
    agent_instance: Optional[BaseHospitalAgent] = None


class AgentRegistry:
    """
    Central registry for all hospital agents
    
    Features:
    - Agent registration and discovery
    - Health monitoring
    - Capability lookup
    - Load balancing
    """
    
    def __init__(self, communication_bus: AgentCommunicationBus):
        self.communication_bus = communication_bus
        self.agents: Dict[str, AgentRegistration] = {}
        self.capability_index: Dict[str, Set[str]] = {}  # capability -> agent_ids
        self.agent_types: Dict[str, Set[str]] = {}  # agent_type -> agent_ids
        
        # Health monitoring
        self.health_check_interval = 30  # seconds
        self.health_check_task: Optional[asyncio.Task] = None
        self.offline_threshold = timedelta(minutes=2)
        
        self.logger = structlog.get_logger("agent_registry")
    
    async def start(self) -> None:
        """Start the agent registry"""
        # Start health monitoring
        self.health_check_task = asyncio.create_task(self._health_monitor())
        self.logger.info("Agent registry started")
    
    async def stop(self) -> None:
        """Stop the agent registry"""
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Agent registry stopped")
    
    async def register_agent(self, agent: BaseHospitalAgent) -> None:
        """
        Register an agent with the registry
        
        Args:
            agent: The agent instance to register
        """
        try:
            # Create registration
            registration = AgentRegistration(
                agent_id=agent.agent_id,
                name=agent.name,
                description=agent.description,
                capabilities=agent.get_capabilities(),
                status=agent.status,
                registered_at=datetime.now(),
                last_heartbeat=datetime.now(),
                agent_instance=agent
            )
            
            # Store in registry
            self.agents[agent.agent_id] = registration
            
            # Update capability index
            for capability in registration.capabilities:
                if capability.name not in self.capability_index:
                    self.capability_index[capability.name] = set()
                self.capability_index[capability.name].add(agent.agent_id)
            
            # Update agent type index
            agent_type = agent.__class__.__name__
            if agent_type not in self.agent_types:
                self.agent_types[agent_type] = set()
            self.agent_types[agent_type].add(agent.agent_id)
            
            # Register with communication bus
            await self.communication_bus.register_agent(
                agent.agent_id,
                {
                    "name": agent.name,
                    "description": agent.description,
                    "capabilities": [cap.dict() for cap in registration.capabilities],
                    "agent_type": agent_type
                },
                agent.process_message
            )
            
            self.logger.info(
                "Agent registered",
                agent_id=agent.agent_id,
                name=agent.name,
                capabilities=len(registration.capabilities)
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to register agent",
                agent_id=agent.agent_id,
                error=str(e)
            )
            raise
    
    async def unregister_agent(self, agent_id: str) -> None:
        """
        Unregister an agent from the registry
        
        Args:
            agent_id: ID of the agent to unregister
        """
        try:
            registration = self.agents.get(agent_id)
            if not registration:
                self.logger.warning("Agent not found for unregistration", agent_id=agent_id)
                return
            
            # Remove from capability index
            for capability in registration.capabilities:
                if capability.name in self.capability_index:
                    self.capability_index[capability.name].discard(agent_id)
                    if not self.capability_index[capability.name]:
                        del self.capability_index[capability.name]
            
            # Remove from agent type index
            agent_type = registration.agent_instance.__class__.__name__ if registration.agent_instance else "Unknown"
            if agent_type in self.agent_types:
                self.agent_types[agent_type].discard(agent_id)
                if not self.agent_types[agent_type]:
                    del self.agent_types[agent_type]
            
            # Remove from registry
            del self.agents[agent_id]
            
            # Unregister from communication bus
            await self.communication_bus.unregister_agent(agent_id)
            
            self.logger.info("Agent unregistered", agent_id=agent_id)
            
        except Exception as e:
            self.logger.error(
                "Failed to unregister agent",
                agent_id=agent_id,
                error=str(e)
            )
    
    def find_agents_by_capability(self, capability_name: str) -> List[AgentRegistration]:
        """
        Find all agents that have a specific capability
        
        Args:
            capability_name: Name of the capability to search for
            
        Returns:
            List of agent registrations that have the capability
        """
        agent_ids = self.capability_index.get(capability_name, set())
        return [self.agents[agent_id] for agent_id in agent_ids if agent_id in self.agents]
    
    def find_agents_by_type(self, agent_type: str) -> List[AgentRegistration]:
        """
        Find all agents of a specific type
        
        Args:
            agent_type: Type of agent to search for
            
        Returns:
            List of agent registrations of the specified type
        """
        agent_ids = self.agent_types.get(agent_type, set())
        return [self.agents[agent_id] for agent_id in agent_ids if agent_id in self.agents]
    
    def get_agent(self, agent_id: str) -> Optional[AgentRegistration]:
        """
        Get agent registration by ID
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            Agent registration if found, None otherwise
        """
        return self.agents.get(agent_id)
    
    def list_all_agents(self) -> List[AgentRegistration]:
        """Get list of all registered agents"""
        return list(self.agents.values())
    
    def get_available_capabilities(self) -> Dict[str, List[str]]:
        """
        Get all available capabilities and the agents that provide them
        
        Returns:
            Dictionary mapping capability names to lists of agent IDs
        """
        return {
            capability: list(agent_ids)
            for capability, agent_ids in self.capability_index.items()
        }
    
    def get_agent_load(self, agent_id: str) -> Optional[float]:
        """
        Get current load of an agent (0.0 to 1.0)
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            Load percentage or None if agent not found
        """
        registration = self.agents.get(agent_id)
        if not registration or not registration.agent_instance:
            return None
        
        agent = registration.agent_instance
        if agent.max_concurrent_tasks == 0:
            return 0.0
        
        return len(agent.active_tasks) / agent.max_concurrent_tasks
    
    def find_least_loaded_agent(self, capability_name: str) -> Optional[str]:
        """
        Find the least loaded agent that has a specific capability
        
        Args:
            capability_name: Name of the capability required
            
        Returns:
            Agent ID of the least loaded agent, or None if no agents available
        """
        candidates = self.find_agents_by_capability(capability_name)
        
        # Filter to only available agents
        available_agents = [
            reg for reg in candidates
            if reg.status == AgentStatus.IDLE or reg.status == AgentStatus.BUSY
        ]
        
        if not available_agents:
            return None
        
        # Find least loaded
        best_agent = None
        best_load = float('inf')
        
        for registration in available_agents:
            load = self.get_agent_load(registration.agent_id)
            if load is not None and load < best_load:
                best_load = load
                best_agent = registration.agent_id
        
        return best_agent
    
    async def _health_monitor(self) -> None:
        """Background task to monitor agent health"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._check_agent_health()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error in health monitor", error=str(e))
    
    async def _check_agent_health(self) -> None:
        """Check health of all registered agents"""
        current_time = datetime.now()
        offline_agents = []
        
        for agent_id, registration in self.agents.items():
            # Check if agent has been silent too long
            time_since_heartbeat = current_time - registration.last_heartbeat
            
            if time_since_heartbeat > self.offline_threshold:
                if registration.status != AgentStatus.OFFLINE:
                    self.logger.warning(
                        "Agent appears offline",
                        agent_id=agent_id,
                        last_heartbeat=registration.last_heartbeat
                    )
                    registration.status = AgentStatus.OFFLINE
                    offline_agents.append(agent_id)
            else:
                # Try to get current status
                try:
                    status = await self.communication_bus.get_agent_status(agent_id)
                    if status:
                        registration.last_heartbeat = current_time
                        if "status" in status:
                            registration.status = AgentStatus(status["status"])
                except Exception:
                    pass
        
        # Broadcast offline events
        for agent_id in offline_agents:
            await self.communication_bus.broadcast_event(
                "agent_offline",
                {"agent_id": agent_id, "timestamp": current_time.isoformat()}
            )
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get statistics about the agent registry"""
        status_counts = {}
        for registration in self.agents.values():
            status = registration.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_agents": len(self.agents),
            "status_distribution": status_counts,
            "capabilities": len(self.capability_index),
            "agent_types": len(self.agent_types),
            "capability_coverage": {
                cap: len(agents) for cap, agents in self.capability_index.items()
            }
        }
