"""
Agent Communication Bus

Handles message routing, delivery, and event broadcasting between agents.
Uses RabbitMQ for reliable message delivery and Redis for caching.
"""

import asyncio
import json
from typing import Any, Callable, Dict, List, Optional, Set
from datetime import datetime, timedelta

import aio_pika
import redis.asyncio as redis
import structlog
from aio_pika import Message, DeliveryMode
from aio_pika.abc import AbstractConnection, AbstractChannel, AbstractQueue

from .base_agent import AgentMessage, MessageType, Priority
from ..messaging.event_bus import EventBus
from ...config.settings import settings

logger = structlog.get_logger(__name__)


class AgentCommunicationBus:
    """
    Manages communication between agents using RabbitMQ and Redis
    
    Features:
    - Reliable message delivery
    - Message routing and queuing
    - Event broadcasting
    - Message persistence and caching
    - Dead letter handling
    """
    
    def __init__(self):
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[AbstractChannel] = None
        self.redis_client: Optional[redis.Redis] = None
        self.event_bus = EventBus()
        
        # Agent registry
        self.registered_agents: Dict[str, Dict[str, Any]] = {}
        self.agent_queues: Dict[str, AbstractQueue] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # Message tracking
        self.pending_responses: Dict[str, asyncio.Future] = {}
        self.message_cache_ttl = 3600  # 1 hour
        
        self.logger = structlog.get_logger("communication_bus")
    
    async def initialize(self) -> None:
        """Initialize connections to RabbitMQ and Redis"""
        try:
            # Connect to RabbitMQ
            self.connection = await aio_pika.connect_robust(settings.rabbitmq_url)
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=100)
            
            # Connect to Redis
            self.redis_client = redis.from_url(settings.redis_url)
            await self.redis_client.ping()
            
            # Setup dead letter exchange
            await self._setup_dead_letter_exchange()
            
            self.logger.info("Communication bus initialized successfully")
            
        except Exception as e:
            self.logger.error("Failed to initialize communication bus", error=str(e))
            raise
    
    async def shutdown(self) -> None:
        """Cleanup connections"""
        if self.connection and not self.connection.is_closed:
            await self.connection.close()
        
        if self.redis_client:
            await self.redis_client.close()
        
        self.logger.info("Communication bus shutdown complete")
    
    async def register_agent(
        self,
        agent_id: str,
        agent_info: Dict[str, Any],
        message_handler: Callable[[AgentMessage], Any]
    ) -> None:
        """
        Register an agent with the communication bus
        
        Args:
            agent_id: Unique identifier for the agent
            agent_info: Agent metadata (name, capabilities, etc.)
            message_handler: Function to handle incoming messages
        """
        try:
            # Store agent info
            self.registered_agents[agent_id] = {
                **agent_info,
                "registered_at": datetime.now(),
                "last_heartbeat": datetime.now()
            }
            
            # Create dedicated queue for the agent
            queue_name = f"agent.{agent_id}"
            queue = await self.channel.declare_queue(
                queue_name,
                durable=True,
                arguments={
                    "x-dead-letter-exchange": "hospital.dlx",
                    "x-message-ttl": 300000,  # 5 minutes
                }
            )
            
            self.agent_queues[agent_id] = queue
            self.message_handlers[agent_id] = message_handler
            
            # Start consuming messages
            await queue.consume(
                callback=lambda msg: self._handle_incoming_message(agent_id, msg),
                no_ack=False
            )
            
            # Cache agent info in Redis
            await self.redis_client.hset(
                "agents:registry",
                agent_id,
                json.dumps(agent_info)
            )
            
            self.logger.info(
                "Agent registered successfully",
                agent_id=agent_id,
                queue_name=queue_name
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to register agent",
                agent_id=agent_id,
                error=str(e)
            )
            raise
    
    async def unregister_agent(self, agent_id: str) -> None:
        """Unregister an agent from the communication bus"""
        try:
            # Remove from registry
            self.registered_agents.pop(agent_id, None)
            self.message_handlers.pop(agent_id, None)
            
            # Delete queue
            if agent_id in self.agent_queues:
                queue = self.agent_queues.pop(agent_id)
                await queue.delete()
            
            # Remove from Redis
            await self.redis_client.hdel("agents:registry", agent_id)
            
            self.logger.info("Agent unregistered", agent_id=agent_id)
            
        except Exception as e:
            self.logger.error(
                "Failed to unregister agent",
                agent_id=agent_id,
                error=str(e)
            )
    
    async def send_message(
        self,
        message: AgentMessage,
        wait_for_response: bool = True,
        timeout_seconds: int = 30
    ) -> Optional[AgentMessage]:
        """
        Send a message to an agent
        
        Args:
            message: The message to send
            wait_for_response: Whether to wait for a response
            timeout_seconds: How long to wait for response
            
        Returns:
            Response message if wait_for_response is True
        """
        try:
            # Validate receiver exists
            if message.receiver_id not in self.registered_agents:
                raise ValueError(f"Agent {message.receiver_id} not registered")
            
            # Serialize message
            message_body = json.dumps({
                "id": message.id,
                "sender_id": message.sender_id,
                "receiver_id": message.receiver_id,
                "message_type": message.message_type.value,
                "priority": message.priority.value,
                "payload": message.payload,
                "timestamp": message.timestamp.isoformat(),
                "correlation_id": message.correlation_id,
                "requires_response": message.requires_response,
                "timeout_seconds": message.timeout_seconds
            })
            
            # Create AMQP message
            amqp_message = Message(
                message_body.encode(),
                delivery_mode=DeliveryMode.PERSISTENT,
                priority=message.priority.value,
                message_id=message.id,
                correlation_id=message.correlation_id,
                timestamp=message.timestamp
            )
            
            # Send to agent's queue
            queue_name = f"agent.{message.receiver_id}"
            await self.channel.default_exchange.publish(
                amqp_message,
                routing_key=queue_name
            )
            
            # Cache message
            await self._cache_message(message)
            
            self.logger.debug(
                "Message sent",
                message_id=message.id,
                receiver=message.receiver_id,
                message_type=message.message_type.value
            )
            
            # Wait for response if required
            if wait_for_response and message.requires_response:
                return await self._wait_for_response(message.id, timeout_seconds)
            
            return None
            
        except Exception as e:
            self.logger.error(
                "Failed to send message",
                message_id=message.id,
                error=str(e)
            )
            raise
    
    async def broadcast_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        target_agents: Optional[List[str]] = None
    ) -> None:
        """
        Broadcast an event to multiple agents
        
        Args:
            event_type: Type of event
            data: Event data
            target_agents: Specific agents to notify (None = all agents)
        """
        try:
            targets = target_agents or list(self.registered_agents.keys())
            
            for agent_id in targets:
                if agent_id in self.registered_agents:
                    message = AgentMessage(
                        sender_id="system",
                        receiver_id=agent_id,
                        message_type=MessageType.NOTIFICATION,
                        payload={
                            "event_type": event_type,
                            "data": data
                        },
                        requires_response=False
                    )
                    
                    await self.send_message(message, wait_for_response=False)
            
            self.logger.info(
                "Event broadcasted",
                event_type=event_type,
                target_count=len(targets)
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to broadcast event",
                event_type=event_type,
                error=str(e)
            )
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of an agent"""
        if agent_id not in self.registered_agents:
            return None
        
        # Send heartbeat request
        heartbeat_message = AgentMessage(
            sender_id="system",
            receiver_id=agent_id,
            message_type=MessageType.HEARTBEAT,
            payload={},
            timeout_seconds=5
        )
        
        try:
            response = await self.send_message(heartbeat_message, timeout_seconds=5)
            if response and response.payload.get("success", False):
                return response.payload
        except Exception:
            pass
        
        return {"status": "offline"}
    
    async def list_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get list of all registered agents"""
        return self.registered_agents.copy()
    
    async def _handle_incoming_message(
        self,
        agent_id: str,
        amqp_message: aio_pika.abc.AbstractIncomingMessage
    ) -> None:
        """Handle incoming AMQP message for an agent"""
        try:
            # Parse message
            message_data = json.loads(amqp_message.body.decode())
            message = AgentMessage(
                id=message_data["id"],
                sender_id=message_data["sender_id"],
                receiver_id=message_data["receiver_id"],
                message_type=MessageType(message_data["message_type"]),
                priority=Priority(message_data["priority"]),
                payload=message_data["payload"],
                timestamp=datetime.fromisoformat(message_data["timestamp"]),
                correlation_id=message_data.get("correlation_id"),
                requires_response=message_data.get("requires_response", True),
                timeout_seconds=message_data.get("timeout_seconds", 30)
            )
            
            # Get message handler
            handler = self.message_handlers.get(agent_id)
            if not handler:
                await amqp_message.nack(requeue=False)
                return
            
            # Process message
            response = await handler(message)
            
            # Send response if required
            if response and message.requires_response:
                await self.send_message(response, wait_for_response=False)
            
            # Handle pending response
            if message.correlation_id and message.correlation_id in self.pending_responses:
                future = self.pending_responses.pop(message.correlation_id)
                if not future.done():
                    future.set_result(message)
            
            # Acknowledge message
            await amqp_message.ack()
            
        except Exception as e:
            self.logger.error(
                "Error handling incoming message",
                agent_id=agent_id,
                error=str(e)
            )
            await amqp_message.nack(requeue=False)
    
    async def _wait_for_response(
        self,
        message_id: str,
        timeout_seconds: int
    ) -> Optional[AgentMessage]:
        """Wait for a response to a message"""
        future = asyncio.Future()
        self.pending_responses[message_id] = future
        
        try:
            response = await asyncio.wait_for(future, timeout=timeout_seconds)
            return response
        except asyncio.TimeoutError:
            self.pending_responses.pop(message_id, None)
            return None
    
    async def _cache_message(self, message: AgentMessage) -> None:
        """Cache message in Redis"""
        try:
            message_data = {
                "id": message.id,
                "sender_id": message.sender_id,
                "receiver_id": message.receiver_id,
                "message_type": message.message_type.value,
                "timestamp": message.timestamp.isoformat(),
                "payload": message.payload
            }
            
            await self.redis_client.setex(
                f"message:{message.id}",
                self.message_cache_ttl,
                json.dumps(message_data)
            )
        except Exception as e:
            self.logger.warning("Failed to cache message", error=str(e))
    
    async def _setup_dead_letter_exchange(self) -> None:
        """Setup dead letter exchange for failed messages"""
        try:
            # Declare dead letter exchange
            dlx = await self.channel.declare_exchange(
                "hospital.dlx",
                aio_pika.ExchangeType.DIRECT,
                durable=True
            )
            
            # Declare dead letter queue
            dlq = await self.channel.declare_queue(
                "hospital.dead_letters",
                durable=True
            )
            
            # Bind queue to exchange
            await dlq.bind(dlx, routing_key="")
            
            self.logger.info("Dead letter exchange setup complete")
            
        except Exception as e:
            self.logger.error("Failed to setup dead letter exchange", error=str(e))
