# 🏥 Multi-Agent Hospital Operations System

A sophisticated multi-agent system for hospital operations with an intelligent agentic chatbot interface, built using **LangGraph**, **CrewAI**, **Google Gemini**, **FastAPI**, **PostgreSQL**, **Redis**, **RabbitMQ**, and **ChromaDB**.

## � Features

### 🤖 **Multi-Agent Architecture**
- **Central Coordinator Agent**: Master orchestrator for all hospital operations
- **Bed Management Agent**: Real-time bed allocation and optimization
- **Patient Care Agent**: Patient admission, care coordination, and treatment planning
- **Emergency Response Agent**: Emergency protocols and crisis management
- **Staff Coordination Agent**: Staff scheduling and workload management
- **Resource Management Agent**: Equipment and supply chain management
- **Pharmacy Agent**: Medication management and drug interactions
- **Laboratory Agent**: Test scheduling and result processing
- **Transport Agent**: Patient and equipment transportation

### � **Agentic Chatbot Interface**
- **Natural Language Processing**: Understand complex hospital operations requests
- **Multi-Agent Coordination**: Automatically coordinate between relevant agents
- **Context-Aware Conversations**: Maintain conversation history and context
- **Role-Based Interactions**: Different capabilities for doctors, nurses, admins, patients
- **Real-Time Workflow Execution**: Execute complex workflows through chat

### 🏗️ **System Architecture**
- **Event-Driven Communication**: RabbitMQ-based reliable messaging
- **Real-Time Coordination**: Async agent communication and workflow orchestration
- **Scalable Design**: Microservices architecture with independent agents
- **Comprehensive Monitoring**: Health checks, metrics, and performance tracking

## �️ Technology Stack

| Component | Technology |
|-----------|------------|
| **Agent Framework** | LangGraph + CrewAI |
| **LLM** | Google Gemini 2.5 Flash |
| **Backend** | FastAPI + AsyncIO |
| **Database** | PostgreSQL + Redis |
| **Message Queue** | RabbitMQ |
| **Vector Store** | ChromaDB |
| **Monitoring** | Prometheus + Grafana |

## � Quick Start

### Prerequisites
- Python 3.9+
- Docker & Docker Compose
- Google Gemini API Key

### 1. Clone and Setup
```bash
git clone <repository-url>
cd multi_agent_hospital

# Create virtual environment
python -m venv hospital_env
source hospital_env/bin/activate  # Linux/Mac
# hospital_env\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# Required: GOOGLE_API_KEY=your_gemini_api_key_here
```

### 3. Start Infrastructure Services
```bash
# Start PostgreSQL, Redis, RabbitMQ
docker-compose up -d postgres redis rabbitmq
```

### 4. Start the Hospital System
```bash
# Development mode with auto-reload
python start_system.py start --dev

# Or run directly
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 5. Access the System
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health
- **Chat API**: http://localhost:8000/api/chat
- **Agent Status**: http://localhost:8000/api/agents

## 💬 Using the Agentic Chatbot

### Example Conversations

#### **Patient Admission**
```
User: "I need to admit a new patient. 65-year-old male with chest pain, emergency admission to cardiology ward."

System: ✅ Patient admission completed successfully!

📋 **Patient ID:** PAT_20250131_143022
🛏️ **Assigned Bed:** CARD-301
📝 **Care Plan:** Created (ID: CP_20250131_143022)

**Next Steps:**
• Immediate vital signs assessment
• Emergency diagnostic tests
• Physician evaluation
• Treatment initiation
```

#### **Bed Availability Check**
```
User: "What beds are available in the ICU? I need one for a critical patient."

System: 🛏️ **Bed Availability - ICU Ward**

📊 **Occupancy Rate:** 66.7%
🟢 **Available Beds:** 2

**Available Options:**
• Room ICU-01 - ICU
• Room ICU-03 - ICU
```

#### **Emergency Response**
```
User: "EMERGENCY: Multiple trauma patients incoming from car accident. Need immediate response protocol."

System: 🚨 **Emergency Response Activated**

🆔 **Response ID:** emergency_trauma_20250131_143045
⚡ **Emergency Type:** Trauma
⏱️ **Estimated Response Time:** 5-10 minutes

**Actions Taken:**
✅ Emergency response protocol activated
✅ Emergency beds reserved
✅ Emergency staff mobilized
✅ Emergency resources prepared
✅ Emergency alert broadcasted

🏥 All relevant departments have been notified and are responding.
```

### API Usage

#### **Start a Chat Session**
```bash
curl -X POST "http://localhost:8000/api/chat/session" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "doctor_001",
    "user_role": "doctor",
    "initial_context": {}
  }'
```

#### **Send a Message**
```bash
curl -X POST "http://localhost:8000/api/chat/message" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Check bed availability in ICU",
    "session_id": "your-session-id",
    "user_id": "doctor_001",
    "user_role": "doctor"
  }'
```

## 🏗️ Architecture Overview

### **Agent Communication Flow**
```mermaid
graph TB
    USER[User/Chatbot] --> COORD[Central Coordinator]
    COORD --> BED[Bed Management Agent]
    COORD --> PATIENT[Patient Care Agent]
    COORD --> STAFF[Staff Coordination Agent]
    COORD --> EMERGENCY[Emergency Response Agent]

    BED --> DB[(Database)]
    PATIENT --> DB
    STAFF --> DB
    EMERGENCY --> DB

    COORD --> RABBITMQ[RabbitMQ]
    RABBITMQ --> BED
    RABBITMQ --> PATIENT
    RABBITMQ --> STAFF
    RABBITMQ --> EMERGENCY
```

### **Key Components**

#### **1. Central Coordinator Agent**
- **Intent Classification**: Understands user requests using Gemini LLM
- **Agent Orchestration**: Routes requests to appropriate specialized agents
- **Workflow Management**: Coordinates complex multi-step operations
- **Response Synthesis**: Combines agent responses into natural language

#### **2. Specialized Agents**
Each agent handles specific hospital operations:
- **Autonomous Decision Making**: Agents make decisions within their domain
- **Inter-Agent Communication**: Coordinate through message passing
- **Real-Time Processing**: Handle requests with appropriate urgency
- **Context Awareness**: Maintain state and context for ongoing operations

#### **3. Communication Infrastructure**
- **RabbitMQ**: Reliable message queuing between agents
- **Redis**: Fast caching and session management
- **PostgreSQL**: Persistent data storage
- **ChromaDB**: Vector storage for AI/ML operations

## 🔧 System Management

### **Using the Management CLI**
```bash
# Start system in development mode
python start_system.py start --dev

# Start only infrastructure services
python start_system.py start --services-only

# Check system status
python start_system.py status

# View logs
python start_system.py logs

# Stop system
python start_system.py stop

# Reset system (removes all data)
python start_system.py reset
```

### **Health Monitoring**
- **Health Check**: `/api/health` - Basic system health
- **Detailed Health**: `/api/health/detailed` - Component-level health
- **Agent Health**: `/api/health/agents` - Individual agent status
- **Readiness Check**: `/api/health/readiness` - Ready to serve requests
- **Liveness Check**: `/api/health/liveness` - Application alive

### **Monitoring Services**
- **Prometheus**: http://localhost:9090 - Metrics collection
- **Grafana**: http://localhost:3000 - Dashboards (admin/admin)
- **RabbitMQ Management**: http://localhost:15672 - Message queue monitoring

## 🧪 Testing

### **Run Example Scenarios**
```bash
# Test patient admission workflow
curl -X POST "http://localhost:8000/api/chat/examples/patient-admission"

# Test bed availability check
curl -X POST "http://localhost:8000/api/chat/examples/bed-availability"

# Test emergency response
curl -X POST "http://localhost:8000/api/chat/examples/emergency-response"
```

### **Agent Testing**
```bash
# Check agent status
curl "http://localhost:8000/api/agents/"

# Get specific agent info
curl "http://localhost:8000/api/agents/bed_management_001"

# Check agent capabilities
curl "http://localhost:8000/api/agents/capabilities/allocate_bed"
```

## 📊 Features in Detail

### **Multi-Agent Coordination**
- **Parallel Processing**: Multiple agents work simultaneously
- **Dependency Management**: Agents coordinate based on dependencies
- **Load Balancing**: Requests distributed across available agents
- **Fault Tolerance**: System continues operating if individual agents fail

### **Intelligent Workflows**
- **Dynamic Routing**: Requests routed based on content and context
- **Context Preservation**: Conversation context maintained across interactions
- **Priority Handling**: Emergency requests get higher priority
- **Workflow Orchestration**: Complex multi-step processes automated

### **Real-Time Operations**
- **Live Updates**: Real-time status updates across the system
- **Event Broadcasting**: Important events broadcast to relevant agents
- **Immediate Response**: Critical operations processed immediately
- **Continuous Monitoring**: System health monitored continuously

## 🔮 Future Enhancements

- **Additional Agents**: Billing, Pharmacy, Laboratory, Transport agents
- **ML Integration**: Predictive analytics for bed allocation and patient flow
- **Mobile Interface**: Mobile app for staff and patients
- **Integration APIs**: Connect with existing hospital systems (EMR, PACS)
- **Advanced Analytics**: Comprehensive reporting and analytics dashboard
- **Voice Interface**: Voice-activated agent interactions
- **Multi-Language Support**: Support for multiple languages

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the health endpoints for system status

---

**🏥 Multi-Agent Hospital System - Transforming healthcare operations with intelligent agent coordination!** ✨
