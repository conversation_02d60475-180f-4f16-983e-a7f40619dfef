"""
Agentic Hospital Chatbot

Intelligent conversational interface that coordinates with multiple hospital agents
to handle complex hospital operations through natural language interactions.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import structlog
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

from ..core.coordinator.central_coordinator import CentralCoordinatorAgent
from ..core.agent_framework.agent_registry import AgentRegistry
from ..core.agent_framework.communication_bus import AgentCommunicationBus
from ..config.settings import settings

logger = structlog.get_logger(__name__)


class ConversationState(Enum):
    """States of conversation flow"""
    GREETING = "greeting"
    LISTENING = "listening"
    PROCESSING = "processing"
    CLARIFYING = "clarifying"
    EXECUTING = "executing"
    RESPONDING = "responding"
    FOLLOW_UP = "follow_up"


@dataclass
class ChatSession:
    """Chat session data"""
    session_id: str
    user_id: str
    user_role: str  # doctor, nurse, admin, patient, visitor
    created_at: datetime
    last_activity: datetime
    conversation_state: ConversationState
    context: Dict[str, Any]
    message_history: List[Dict[str, Any]]
    active_workflows: List[str]


@dataclass
class ChatMessage:
    """Chat message structure"""
    message_id: str
    session_id: str
    user_id: str
    message: str
    timestamp: datetime
    message_type: str  # user, assistant, system
    metadata: Dict[str, Any]


@dataclass
class ChatResponse:
    """Chat response structure"""
    message_id: str
    response: str
    actions_taken: List[str]
    follow_up_needed: bool
    follow_up_actions: List[str]
    execution_time_seconds: float
    involved_agents: List[str]
    success: bool
    metadata: Dict[str, Any]


class AgenticHospitalChatBot:
    """
    Intelligent hospital chatbot that coordinates with multiple agents
    
    Features:
    - Natural language understanding
    - Multi-agent coordination
    - Context-aware conversations
    - Role-based interactions
    - Workflow management
    - Real-time hospital operations
    """
    
    def __init__(
        self,
        coordinator: CentralCoordinatorAgent,
        agent_registry: AgentRegistry,
        communication_bus: AgentCommunicationBus
    ):
        self.coordinator = coordinator
        self.agent_registry = agent_registry
        self.communication_bus = communication_bus
        
        # LLM for conversation management
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-pro",
            google_api_key=settings.google_api_key,
            temperature=0.3
        )
        
        # Session management
        self.active_sessions: Dict[str, ChatSession] = {}
        self.session_timeout_minutes = 30
        
        # Conversation templates
        self.greeting_templates = {
            "doctor": "Hello Doctor! I'm your AI assistant for hospital operations. How can I help you today?",
            "nurse": "Hi! I'm here to assist with patient care and hospital operations. What do you need help with?",
            "admin": "Welcome! I can help you with hospital administration, resource management, and operational tasks.",
            "patient": "Hello! I'm here to help answer questions about your care and hospital services.",
            "visitor": "Welcome to the hospital! I can provide information about visiting hours, directions, and general services.",
            "default": "Hello! I'm your hospital AI assistant. I can help with patient care, bed management, emergencies, and general hospital operations. How may I assist you?"
        }
        
        # Role-based capabilities
        self.role_capabilities = {
            "doctor": [
                "patient_admission", "treatment_coordination", "discharge_planning",
                "bed_allocation", "emergency_response", "resource_management"
            ],
            "nurse": [
                "patient_care", "bed_management", "treatment_coordination",
                "resource_requests", "emergency_assistance"
            ],
            "admin": [
                "resource_management", "staff_coordination", "operational_optimization",
                "reporting", "system_management"
            ],
            "patient": [
                "general_inquiry", "appointment_scheduling", "service_information"
            ],
            "visitor": [
                "general_inquiry", "directions", "visiting_information"
            ]
        }
        
        self.logger = structlog.get_logger("agentic_chatbot")
    
    async def start_session(
        self,
        user_id: str,
        user_role: str = "default",
        initial_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Start a new chat session"""
        
        session_id = str(uuid.uuid4())
        
        session = ChatSession(
            session_id=session_id,
            user_id=user_id,
            user_role=user_role,
            created_at=datetime.now(),
            last_activity=datetime.now(),
            conversation_state=ConversationState.GREETING,
            context=initial_context or {},
            message_history=[],
            active_workflows=[]
        )
        
        self.active_sessions[session_id] = session
        
        self.logger.info(
            "Chat session started",
            session_id=session_id,
            user_id=user_id,
            user_role=user_role
        )
        
        return session_id
    
    async def process_message(
        self,
        session_id: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ChatResponse:
        """Process incoming chat message"""
        
        start_time = datetime.now()
        message_id = str(uuid.uuid4())
        
        try:
            # Get or create session
            session = await self._get_or_create_session(session_id)
            
            # Update session activity
            session.last_activity = datetime.now()
            session.conversation_state = ConversationState.PROCESSING
            
            # Create chat message
            chat_message = ChatMessage(
                message_id=message_id,
                session_id=session_id,
                user_id=session.user_id,
                message=message,
                timestamp=datetime.now(),
                message_type="user",
                metadata=metadata or {}
            )
            
            # Add to message history
            session.message_history.append({
                "message_id": message_id,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                "type": "user",
                "metadata": metadata or {}
            })
            
            # Process the message
            if session.conversation_state == ConversationState.GREETING:
                response = await self._handle_greeting(session, message)
            else:
                response = await self._handle_conversation(session, chat_message)
            
            # Update conversation state
            session.conversation_state = ConversationState.LISTENING
            
            # Add response to history
            session.message_history.append({
                "message_id": response.message_id,
                "message": response.response,
                "timestamp": datetime.now().isoformat(),
                "type": "assistant",
                "metadata": response.metadata
            })
            
            # Clean up old sessions
            await self._cleanup_old_sessions()
            
            execution_time = (datetime.now() - start_time).total_seconds()
            response.execution_time_seconds = execution_time
            
            return response
            
        except Exception as e:
            self.logger.error(
                "Error processing chat message",
                session_id=session_id,
                message=message,
                error=str(e)
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ChatResponse(
                message_id=message_id,
                response="I apologize, but I encountered an error processing your message. Please try again or contact support if the issue persists.",
                actions_taken=[],
                follow_up_needed=False,
                follow_up_actions=[],
                execution_time_seconds=execution_time,
                involved_agents=[],
                success=False,
                metadata={"error": str(e)}
            )
    
    async def _get_or_create_session(self, session_id: str) -> ChatSession:
        """Get existing session or create new one"""
        
        if session_id in self.active_sessions:
            return self.active_sessions[session_id]
        
        # Create new session with default values
        session = ChatSession(
            session_id=session_id,
            user_id="anonymous",
            user_role="default",
            created_at=datetime.now(),
            last_activity=datetime.now(),
            conversation_state=ConversationState.GREETING,
            context={},
            message_history=[],
            active_workflows=[]
        )
        
        self.active_sessions[session_id] = session
        return session
    
    async def _handle_greeting(self, session: ChatSession, message: str) -> ChatResponse:
        """Handle initial greeting and role detection"""
        
        # Detect user role from message if not set
        if session.user_role == "default":
            session.user_role = await self._detect_user_role(message)
        
        # Generate greeting response
        greeting = self.greeting_templates.get(session.user_role, self.greeting_templates["default"])
        
        # Add role-specific capabilities info
        capabilities = self.role_capabilities.get(session.user_role, [])
        if capabilities:
            greeting += f"\n\nI can help you with: {', '.join(capabilities)}"
        
        return ChatResponse(
            message_id=str(uuid.uuid4()),
            response=greeting,
            actions_taken=["Session initialized", "User role detected"],
            follow_up_needed=True,
            follow_up_actions=["Await user request"],
            execution_time_seconds=0.1,
            involved_agents=[],
            success=True,
            metadata={"user_role": session.user_role, "capabilities": capabilities}
        )
    
    async def _handle_conversation(self, session: ChatSession, chat_message: ChatMessage) -> ChatResponse:
        """Handle ongoing conversation"""
        
        # Check if user has permission for requested action
        if not await self._check_user_permissions(session, chat_message.message):
            return ChatResponse(
                message_id=str(uuid.uuid4()),
                response="I'm sorry, but you don't have permission to perform that action. Please contact an administrator if you need additional access.",
                actions_taken=["Permission check failed"],
                follow_up_needed=False,
                follow_up_actions=[],
                execution_time_seconds=0.1,
                involved_agents=[],
                success=False,
                metadata={"permission_denied": True}
            )
        
        # Enhance message with session context
        enhanced_context = {
            **session.context,
            "user_role": session.user_role,
            "session_history": session.message_history[-5:],  # Last 5 messages
            "active_workflows": session.active_workflows
        }
        
        # Process through coordinator
        coordinator_response = await self.coordinator.process_user_message(
            user_message=chat_message.message,
            context=enhanced_context,
            user_id=session.user_id
        )
        
        # Update session context with any new information
        session.context.update(coordinator_response.get("context_updates", {}))
        
        # Track active workflows
        if coordinator_response.get("workflow_id"):
            session.active_workflows.append(coordinator_response["workflow_id"])
        
        # Generate enhanced response with conversation context
        enhanced_response = await self._enhance_response_with_context(
            session, coordinator_response["response"]
        )
        
        return ChatResponse(
            message_id=str(uuid.uuid4()),
            response=enhanced_response,
            actions_taken=coordinator_response.get("actions_taken", []),
            follow_up_needed=coordinator_response.get("follow_up_needed", False),
            follow_up_actions=coordinator_response.get("follow_up_actions", []),
            execution_time_seconds=coordinator_response.get("execution_time_seconds", 0),
            involved_agents=coordinator_response.get("involved_agents", []),
            success=coordinator_response.get("success", True),
            metadata={
                "request_id": coordinator_response.get("request_id"),
                "user_role": session.user_role,
                "session_context": session.context
            }
        )
    
    async def _detect_user_role(self, message: str) -> str:
        """Detect user role from initial message"""
        
        message_lower = message.lower()
        
        # Simple keyword-based detection
        if any(word in message_lower for word in ["doctor", "physician", "dr.", "md"]):
            return "doctor"
        elif any(word in message_lower for word in ["nurse", "rn", "nursing"]):
            return "nurse"
        elif any(word in message_lower for word in ["admin", "administrator", "manager"]):
            return "admin"
        elif any(word in message_lower for word in ["patient", "my treatment", "my care"]):
            return "patient"
        elif any(word in message_lower for word in ["visitor", "visiting", "family member"]):
            return "visitor"
        
        return "default"
    
    async def _check_user_permissions(self, session: ChatSession, message: str) -> bool:
        """Check if user has permission for requested action"""
        
        # Simple permission check based on role
        # In production, this would integrate with proper authentication/authorization
        
        message_lower = message.lower()
        user_role = session.user_role
        
        # Restricted actions for patients and visitors
        if user_role in ["patient", "visitor"]:
            restricted_keywords = [
                "admit patient", "discharge", "assign bed", "emergency protocol",
                "staff schedule", "medication order", "surgery"
            ]
            
            if any(keyword in message_lower for keyword in restricted_keywords):
                return False
        
        return True
    
    async def _enhance_response_with_context(self, session: ChatSession, base_response: str) -> str:
        """Enhance response with conversational context"""
        
        # Add personalization based on user role
        if session.user_role == "doctor":
            if "patient" in base_response.lower():
                base_response += "\n\n💡 *As a physician, you can also ask me about treatment protocols, discharge planning, or emergency procedures.*"
        
        elif session.user_role == "nurse":
            if "bed" in base_response.lower() or "patient" in base_response.lower():
                base_response += "\n\n💡 *Need help with patient care coordination or medication administration? Just ask!*"
        
        # Add follow-up suggestions based on context
        if session.active_workflows:
            base_response += f"\n\n📋 *You have {len(session.active_workflows)} active workflow(s). Say 'status' to check progress.*"
        
        return base_response
    
    async def _cleanup_old_sessions(self) -> None:
        """Clean up inactive sessions"""
        
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.active_sessions.items():
            time_since_activity = current_time - session.last_activity
            
            if time_since_activity.total_seconds() > (self.session_timeout_minutes * 60):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.active_sessions[session_id]
            self.logger.info("Session expired and cleaned up", session_id=session_id)
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        
        session = self.active_sessions.get(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "user_role": session.user_role,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "conversation_state": session.conversation_state.value,
            "message_count": len(session.message_history),
            "active_workflows": session.active_workflows,
            "context_keys": list(session.context.keys())
        }
    
    async def end_session(self, session_id: str) -> bool:
        """End a chat session"""
        
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            del self.active_sessions[session_id]
            
            self.logger.info(
                "Session ended",
                session_id=session_id,
                user_id=session.user_id,
                duration_minutes=(datetime.now() - session.created_at).total_seconds() / 60
            )
            
            return True
        
        return False
    
    def get_active_sessions_count(self) -> int:
        """Get count of active sessions"""
        return len(self.active_sessions)
    
    async def broadcast_system_message(self, message: str, target_roles: Optional[List[str]] = None) -> int:
        """Broadcast system message to active sessions"""
        
        count = 0
        
        for session in self.active_sessions.values():
            if target_roles is None or session.user_role in target_roles:
                # Add system message to session history
                session.message_history.append({
                    "message_id": str(uuid.uuid4()),
                    "message": f"🔔 **System Notification:** {message}",
                    "timestamp": datetime.now().isoformat(),
                    "type": "system",
                    "metadata": {"broadcast": True}
                })
                count += 1
        
        return count
