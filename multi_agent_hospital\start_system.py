#!/usr/bin/env python3
"""
Multi-Agent Hospital System Startup Script

This script starts the complete hospital system with all dependencies.
"""

import asyncio
import os
import sys
import subprocess
import time
from pathlib import Path

import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

console = Console()
app = typer.Typer(help="Multi-Agent Hospital System Management")


def check_dependencies():
    """Check if required dependencies are available"""
    dependencies = {
        "docker": "Docker is required for running services",
        "docker-compose": "Docker Compose is required for orchestration"
    }
    
    missing = []
    for cmd, description in dependencies.items():
        try:
            subprocess.run([cmd, "--version"], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing.append((cmd, description))
    
    if missing:
        console.print("[red]Missing dependencies:[/red]")
        for cmd, desc in missing:
            console.print(f"  • {cmd}: {desc}")
        return False
    
    return True


def check_env_file():
    """Check if .env file exists and create from template if not"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            console.print("[yellow]Creating .env file from template...[/yellow]")
            env_file.write_text(env_example.read_text())
            console.print("[green]✓ .env file created[/green]")
            console.print("[yellow]Please edit .env file with your configuration[/yellow]")
        else:
            console.print("[red]No .env file found and no template available[/red]")
            return False
    
    return True


@app.command()
def start(
    services_only: bool = typer.Option(False, "--services-only", help="Start only infrastructure services"),
    dev: bool = typer.Option(False, "--dev", help="Start in development mode"),
    port: int = typer.Option(8000, "--port", help="API port")
):
    """Start the Multi-Agent Hospital System"""
    
    console.print(Panel.fit(
        "[bold blue]🏥 Multi-Agent Hospital System[/bold blue]\n"
        "Starting intelligent hospital operations platform...",
        border_style="blue"
    ))
    
    # Check dependencies
    if not check_dependencies():
        raise typer.Exit(1)
    
    # Check environment file
    if not check_env_file():
        raise typer.Exit(1)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        # Start infrastructure services
        task1 = progress.add_task("Starting infrastructure services...", total=None)
        try:
            subprocess.run(
                ["docker-compose", "up", "-d", "postgres", "redis", "rabbitmq"],
                check=True,
                capture_output=True
            )
            progress.update(task1, description="✓ Infrastructure services started")
        except subprocess.CalledProcessError as e:
            console.print(f"[red]Failed to start services: {e}[/red]")
            raise typer.Exit(1)
        
        if not services_only:
            # Wait for services to be ready
            task2 = progress.add_task("Waiting for services to be ready...", total=None)
            time.sleep(10)  # Give services time to start
            progress.update(task2, description="✓ Services ready")
            
            # Start the application
            task3 = progress.add_task("Starting hospital system...", total=None)
            
            try:
                if dev:
                    # Development mode with auto-reload
                    os.environ["DEBUG"] = "True"
                    os.environ["API_PORT"] = str(port)
                    
                    import uvicorn
                    from main import app as fastapi_app
                    
                    progress.update(task3, description="✓ Starting in development mode")
                    progress.stop()
                    
                    console.print(f"\n[green]🚀 Hospital System starting on http://localhost:{port}[/green]")
                    console.print(f"[green]📚 API Documentation: http://localhost:{port}/docs[/green]")
                    console.print(f"[green]🔍 Health Check: http://localhost:{port}/api/health[/green]")
                    
                    uvicorn.run(
                        "main:app",
                        host="0.0.0.0",
                        port=port,
                        reload=True,
                        log_level="info"
                    )
                else:
                    # Production mode
                    progress.update(task3, description="✓ Hospital system started")
                    progress.stop()
                    
                    console.print(f"\n[green]🚀 Hospital System started on http://localhost:{port}[/green]")
                    
                    # Run the application
                    asyncio.run(main())
                    
            except KeyboardInterrupt:
                console.print("\n[yellow]Shutting down...[/yellow]")
            except Exception as e:
                console.print(f"[red]Failed to start application: {e}[/red]")
                raise typer.Exit(1)


@app.command()
def stop():
    """Stop the Multi-Agent Hospital System"""
    
    console.print("[yellow]Stopping Multi-Agent Hospital System...[/yellow]")
    
    try:
        subprocess.run(["docker-compose", "down"], check=True)
        console.print("[green]✓ System stopped successfully[/green]")
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Failed to stop system: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def status():
    """Check system status"""
    
    console.print("[blue]Checking system status...[/blue]")
    
    try:
        result = subprocess.run(
            ["docker-compose", "ps"],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Parse docker-compose ps output
        lines = result.stdout.strip().split('\n')
        if len(lines) <= 1:
            console.print("[yellow]No services running[/yellow]")
            return
        
        table = Table(title="Service Status")
        table.add_column("Service", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Ports", style="blue")
        
        for line in lines[1:]:  # Skip header
            parts = line.split()
            if len(parts) >= 3:
                service = parts[0]
                status = "Running" if "Up" in line else "Stopped"
                ports = " ".join(parts[4:]) if len(parts) > 4 else "N/A"
                
                status_style = "green" if status == "Running" else "red"
                table.add_row(service, f"[{status_style}]{status}[/{status_style}]", ports)
        
        console.print(table)
        
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Failed to check status: {e}[/red]")


@app.command()
def logs(
    service: str = typer.Argument(None, help="Service name to show logs for"),
    follow: bool = typer.Option(False, "--follow", "-f", help="Follow log output")
):
    """Show system logs"""
    
    cmd = ["docker-compose", "logs"]
    
    if follow:
        cmd.append("-f")
    
    if service:
        cmd.append(service)
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        pass


@app.command()
def reset():
    """Reset the system (stop and remove all data)"""
    
    if not typer.confirm("This will remove all data. Are you sure?"):
        raise typer.Abort()
    
    console.print("[yellow]Resetting system...[/yellow]")
    
    try:
        subprocess.run(["docker-compose", "down", "-v"], check=True)
        console.print("[green]✓ System reset successfully[/green]")
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Failed to reset system: {e}[/red]")
        raise typer.Exit(1)


async def main():
    """Main application entry point"""
    from main import main as app_main
    await app_main()


if __name__ == "__main__":
    app()
