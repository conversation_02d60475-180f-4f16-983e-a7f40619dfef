from _typeshed import Incomplete
from typing import ClassVar

from ..cmd import Command

class install_egg_info(Command):
    description: ClassVar[str]
    user_options: ClassVar[list[tuple[str, str, str]]]
    install_dir: Incomplete
    def initialize_options(self) -> None: ...
    target: Incomplete
    outputs: Incomplete
    def finalize_options(self) -> None: ...
    def run(self) -> None: ...
    def get_outputs(self) -> list[str]: ...

def safe_name(name): ...
def safe_version(version): ...
def to_filename(name): ...
