"""
Bed Repository

Data access layer for bed management operations.
For now, this uses in-memory data structures. In production,
this would connect to PostgreSQL database.
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import structlog

logger = structlog.get_logger(__name__)


class BedStatus(Enum):
    """Bed status enumeration"""
    AVAILABLE = "available"
    OCCUPIED = "occupied"
    CLEANING = "cleaning"
    MAINTENANCE = "maintenance"
    RESERVED = "reserved"


class RoomType(Enum):
    """Room type enumeration"""
    PRIVATE = "private"
    SEMI_PRIVATE = "semi_private"
    WARD = "ward"
    ICU = "icu"
    ISOLATION = "isolation"


@dataclass
class Bed:
    """Bed data model"""
    id: str
    room_number: str
    ward: str
    room_type: RoomType
    status: BedStatus
    special_features: List[str]
    current_patient_id: Optional[str] = None
    assigned_at: Optional[datetime] = None
    last_cleaned: Optional[datetime] = None
    created_at: datetime = None
    updated_at: datetime = None


class BedRepository:
    """Repository for bed data operations"""
    
    def __init__(self):
        # In-memory data store (would be database in production)
        self.beds: Dict[str, Bed] = {}
        self.waiting_list: List[Dict[str, Any]] = []
        self._initialize_sample_data()
    
    def _initialize_sample_data(self):
        """Initialize with sample bed data"""
        sample_beds = [
            # ICU Beds
            Bed("ICU-001", "ICU-01", "icu", RoomType.ICU, BedStatus.AVAILABLE, 
                 ["ventilator", "cardiac_monitor", "isolation_capable"]),
            Bed("ICU-002", "ICU-02", "icu", RoomType.ICU, BedStatus.OCCUPIED, 
                 ["ventilator", "cardiac_monitor"], "PAT_001"),
            Bed("ICU-003", "ICU-03", "icu", RoomType.ICU, BedStatus.AVAILABLE, 
                 ["ventilator", "cardiac_monitor", "dialysis_capable"]),
            
            # General Medicine Beds
            Bed("MED-001", "MED-101", "general_medicine", RoomType.PRIVATE, BedStatus.AVAILABLE, 
                 ["oxygen", "nurse_call"]),
            Bed("MED-002", "MED-102", "general_medicine", RoomType.PRIVATE, BedStatus.OCCUPIED, 
                 ["oxygen", "nurse_call"], "PAT_002"),
            Bed("MED-003", "MED-103", "general_medicine", RoomType.SEMI_PRIVATE, BedStatus.AVAILABLE, 
                 ["oxygen", "nurse_call"]),
            Bed("MED-004", "MED-104", "general_medicine", RoomType.SEMI_PRIVATE, BedStatus.CLEANING, 
                 ["oxygen", "nurse_call"]),
            
            # Surgical Beds
            Bed("SURG-001", "SURG-201", "surgical", RoomType.PRIVATE, BedStatus.AVAILABLE, 
                 ["post_op_monitoring", "pain_management"]),
            Bed("SURG-002", "SURG-202", "surgical", RoomType.PRIVATE, BedStatus.OCCUPIED, 
                 ["post_op_monitoring", "pain_management"], "PAT_003"),
            
            # Cardiology Beds
            Bed("CARD-001", "CARD-301", "cardiology", RoomType.PRIVATE, BedStatus.AVAILABLE, 
                 ["cardiac_monitor", "telemetry", "oxygen"]),
            Bed("CARD-002", "CARD-302", "cardiology", RoomType.PRIVATE, BedStatus.AVAILABLE, 
                 ["cardiac_monitor", "telemetry", "oxygen"]),
            
            # Pediatrics Beds
            Bed("PED-001", "PED-401", "pediatrics", RoomType.PRIVATE, BedStatus.AVAILABLE, 
                 ["pediatric_equipment", "family_accommodation"]),
            Bed("PED-002", "PED-402", "pediatrics", RoomType.WARD, BedStatus.AVAILABLE, 
                 ["pediatric_equipment"]),
        ]
        
        for bed in sample_beds:
            bed.created_at = datetime.now()
            bed.updated_at = datetime.now()
            if bed.status == BedStatus.OCCUPIED:
                bed.assigned_at = datetime.now() - timedelta(hours=2)
            self.beds[bed.id] = bed
        
        logger.info(f"Initialized {len(sample_beds)} sample beds")
    
    async def get_available_beds(
        self,
        ward_type: Optional[str] = None,
        room_type: Optional[str] = None,
        special_requirements: Optional[List[str]] = None,
        isolation_required: bool = False,
        exclude_reserved: bool = False
    ) -> List[Bed]:
        """Get available beds based on criteria"""
        
        available_beds = []
        
        for bed in self.beds.values():
            # Check availability
            if exclude_reserved:
                if bed.status not in [BedStatus.AVAILABLE]:
                    continue
            else:
                if bed.status not in [BedStatus.AVAILABLE, BedStatus.RESERVED]:
                    continue
            
            # Check ward type
            if ward_type and bed.ward != ward_type:
                continue
            
            # Check room type
            if room_type and bed.room_type.value != room_type:
                continue
            
            # Check isolation requirement
            if isolation_required and "isolation_capable" not in bed.special_features:
                continue
            
            # Check special requirements
            if special_requirements:
                if not all(req in bed.special_features for req in special_requirements):
                    continue
            
            available_beds.append(bed)
        
        # Sort by preference (private rooms first, then by room number)
        available_beds.sort(key=lambda b: (
            0 if b.room_type == RoomType.PRIVATE else 1,
            b.room_number
        ))
        
        return available_beds
    
    async def get_ward_capacity(self, ward_type: str) -> int:
        """Get total capacity for a ward"""
        return sum(1 for bed in self.beds.values() if bed.ward == ward_type)
    
    async def get_occupied_bed_count(self, ward_type: str) -> int:
        """Get count of occupied beds in a ward"""
        return sum(
            1 for bed in self.beds.values() 
            if bed.ward == ward_type and bed.status == BedStatus.OCCUPIED
        )
    
    async def assign_bed(
        self,
        bed_id: str,
        patient_id: str,
        assigned_at: Optional[datetime] = None
    ) -> bool:
        """Assign a bed to a patient"""
        
        if bed_id not in self.beds:
            return False
        
        bed = self.beds[bed_id]
        
        if bed.status != BedStatus.AVAILABLE:
            return False
        
        bed.status = BedStatus.OCCUPIED
        bed.current_patient_id = patient_id
        bed.assigned_at = assigned_at or datetime.now()
        bed.updated_at = datetime.now()
        
        logger.info(
            "Bed assigned",
            bed_id=bed_id,
            patient_id=patient_id,
            room_number=bed.room_number
        )
        
        return True
    
    async def release_bed(self, bed_id: str) -> bool:
        """Release a bed (patient discharged)"""
        
        if bed_id not in self.beds:
            return False
        
        bed = self.beds[bed_id]
        
        if bed.status != BedStatus.OCCUPIED:
            return False
        
        bed.status = BedStatus.CLEANING  # Needs cleaning before next patient
        bed.current_patient_id = None
        bed.assigned_at = None
        bed.updated_at = datetime.now()
        
        logger.info("Bed released for cleaning", bed_id=bed_id, room_number=bed.room_number)
        
        return True
    
    async def mark_bed_cleaned(self, bed_id: str) -> bool:
        """Mark bed as cleaned and available"""
        
        if bed_id not in self.beds:
            return False
        
        bed = self.beds[bed_id]
        
        if bed.status != BedStatus.CLEANING:
            return False
        
        bed.status = BedStatus.AVAILABLE
        bed.last_cleaned = datetime.now()
        bed.updated_at = datetime.now()
        
        logger.info("Bed cleaned and available", bed_id=bed_id, room_number=bed.room_number)
        
        return True
    
    async def get_transfer_candidates(
        self,
        ward_type: str,
        min_priority_level: str = "low"
    ) -> List[Bed]:
        """Get beds with patients who could be transferred"""
        
        # This would implement logic to find patients who could be moved
        # For now, return empty list (placeholder)
        return []
    
    async def get_adjacent_wards(self, ward_type: str) -> List[str]:
        """Get adjacent wards that could accommodate overflow"""
        
        # Simple mapping of adjacent wards
        adjacent_mapping = {
            "icu": ["cardiology", "surgical"],
            "cardiology": ["icu", "general_medicine"],
            "surgical": ["icu", "general_medicine"],
            "general_medicine": ["cardiology", "surgical"],
            "pediatrics": ["general_medicine"]
        }
        
        return adjacent_mapping.get(ward_type, [])
    
    async def reserve_emergency_beds(
        self,
        emergency_type: str,
        bed_count: int,
        duration_hours: int = 24
    ) -> List[Bed]:
        """Reserve beds for emergency situations"""
        
        # Find available beds suitable for emergency
        available_beds = await self.get_available_beds()
        
        # Prioritize ICU and cardiology beds for emergencies
        emergency_suitable = []
        for bed in available_beds:
            if bed.ward in ["icu", "cardiology", "surgical"]:
                emergency_suitable.append(bed)
        
        # Reserve the requested number of beds
        reserved_beds = []
        for i, bed in enumerate(emergency_suitable[:bed_count]):
            bed.status = BedStatus.RESERVED
            bed.updated_at = datetime.now()
            reserved_beds.append(bed)
            
            logger.info(
                "Bed reserved for emergency",
                bed_id=bed.id,
                emergency_type=emergency_type,
                duration_hours=duration_hours
            )
        
        return reserved_beds
    
    async def add_to_waiting_list(
        self,
        patient_id: str,
        ward_type: str,
        priority: str
    ) -> None:
        """Add patient to waiting list"""
        
        waiting_entry = {
            "patient_id": patient_id,
            "ward_type": ward_type,
            "priority": priority,
            "added_at": datetime.now(),
            "position": len(self.waiting_list) + 1
        }
        
        self.waiting_list.append(waiting_entry)
        
        logger.info(
            "Patient added to waiting list",
            patient_id=patient_id,
            ward_type=ward_type,
            priority=priority,
            position=waiting_entry["position"]
        )
    
    async def get_waiting_list(self, ward_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get waiting list, optionally filtered by ward type"""
        
        if ward_type:
            return [entry for entry in self.waiting_list if entry["ward_type"] == ward_type]
        
        return self.waiting_list.copy()
    
    async def get_bed_by_id(self, bed_id: str) -> Optional[Bed]:
        """Get bed by ID"""
        return self.beds.get(bed_id)
    
    async def get_beds_by_patient(self, patient_id: str) -> List[Bed]:
        """Get beds assigned to a patient"""
        return [
            bed for bed in self.beds.values()
            if bed.current_patient_id == patient_id
        ]
    
    async def get_ward_summary(self, ward_type: str) -> Dict[str, Any]:
        """Get summary statistics for a ward"""
        
        ward_beds = [bed for bed in self.beds.values() if bed.ward == ward_type]
        
        if not ward_beds:
            return {
                "ward_type": ward_type,
                "total_beds": 0,
                "available": 0,
                "occupied": 0,
                "cleaning": 0,
                "maintenance": 0,
                "reserved": 0,
                "occupancy_rate": 0.0
            }
        
        status_counts = {}
        for status in BedStatus:
            status_counts[status.value] = sum(
                1 for bed in ward_beds if bed.status == status
            )
        
        total_beds = len(ward_beds)
        occupied_beds = status_counts.get("occupied", 0)
        occupancy_rate = occupied_beds / total_beds if total_beds > 0 else 0
        
        return {
            "ward_type": ward_type,
            "total_beds": total_beds,
            "available": status_counts.get("available", 0),
            "occupied": occupied_beds,
            "cleaning": status_counts.get("cleaning", 0),
            "maintenance": status_counts.get("maintenance", 0),
            "reserved": status_counts.get("reserved", 0),
            "occupancy_rate": round(occupancy_rate, 2)
        }
