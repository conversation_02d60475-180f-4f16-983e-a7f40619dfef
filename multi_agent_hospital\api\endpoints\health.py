"""
Health Check API Endpoints

System health monitoring and status endpoints.
"""

from datetime import datetime
from typing import Dict, Any

import structlog
from fastapi import APIRouter, HTTPException, Depends, Request

logger = structlog.get_logger(__name__)

router = APIRouter()


def get_hospital_system(request: Request):
    """Dependency to get hospital system"""
    if not hasattr(request.app.state, 'hospital_system'):
        raise HTTPException(status_code=503, detail="Hospital system not available")
    return request.app.state.hospital_system


@router.get("/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Multi-Agent Hospital System"
    }


@router.get("/detailed")
async def detailed_health_check(
    hospital_system = Depends(get_hospital_system)
):
    """Detailed health check with system components"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }
        
        # Check if system is running
        if not hospital_system.running:
            health_status["status"] = "unhealthy"
            health_status["components"]["system"] = {
                "status": "down",
                "message": "Hospital system not running"
            }
            return health_status
        
        # Check communication bus
        try:
            if hospital_system.communication_bus:
                health_status["components"]["communication_bus"] = {
                    "status": "healthy",
                    "message": "Communication bus operational"
                }
            else:
                health_status["components"]["communication_bus"] = {
                    "status": "unhealthy",
                    "message": "Communication bus not initialized"
                }
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["communication_bus"] = {
                "status": "unhealthy",
                "message": f"Communication bus error: {str(e)}"
            }
            health_status["status"] = "degraded"
        
        # Check agent registry
        try:
            if hospital_system.agent_registry:
                stats = hospital_system.agent_registry.get_registry_stats()
                health_status["components"]["agent_registry"] = {
                    "status": "healthy",
                    "message": f"{stats['total_agents']} agents registered",
                    "details": stats
                }
            else:
                health_status["components"]["agent_registry"] = {
                    "status": "unhealthy",
                    "message": "Agent registry not initialized"
                }
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["agent_registry"] = {
                "status": "unhealthy",
                "message": f"Agent registry error: {str(e)}"
            }
            health_status["status"] = "degraded"
        
        # Check coordinator
        try:
            if hospital_system.coordinator:
                coordinator_status = hospital_system.coordinator.get_status()
                health_status["components"]["coordinator"] = {
                    "status": "healthy" if coordinator_status["status"] != "error" else "unhealthy",
                    "message": "Central coordinator operational",
                    "details": coordinator_status
                }
            else:
                health_status["components"]["coordinator"] = {
                    "status": "unhealthy",
                    "message": "Central coordinator not initialized"
                }
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["coordinator"] = {
                "status": "unhealthy",
                "message": f"Coordinator error: {str(e)}"
            }
            health_status["status"] = "degraded"
        
        # Check chatbot
        try:
            if hospital_system.chatbot:
                active_sessions = hospital_system.chatbot.get_active_sessions_count()
                health_status["components"]["chatbot"] = {
                    "status": "healthy",
                    "message": f"Chatbot operational with {active_sessions} active sessions",
                    "details": {"active_sessions": active_sessions}
                }
            else:
                health_status["components"]["chatbot"] = {
                    "status": "unhealthy",
                    "message": "Chatbot not initialized"
                }
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["chatbot"] = {
                "status": "unhealthy",
                "message": f"Chatbot error: {str(e)}"
            }
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error("Error in detailed health check", error=str(e))
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.get("/agents")
async def agents_health_check(
    hospital_system = Depends(get_hospital_system)
):
    """Health check for all agents"""
    try:
        if not hospital_system.agent_registry:
            raise HTTPException(status_code=503, detail="Agent registry not available")
        
        agents = hospital_system.agent_registry.list_all_agents()
        agent_health = []
        
        for registration in agents:
            try:
                # Get agent status
                status = await hospital_system.communication_bus.get_agent_status(registration.agent_id)
                
                agent_health.append({
                    "agent_id": registration.agent_id,
                    "name": registration.name,
                    "status": status.get("status", "unknown") if status else "offline",
                    "active_tasks": status.get("active_tasks", 0) if status else 0,
                    "last_heartbeat": registration.last_heartbeat.isoformat(),
                    "health": "healthy" if status and status.get("status") in ["idle", "busy"] else "unhealthy"
                })
                
            except Exception as e:
                agent_health.append({
                    "agent_id": registration.agent_id,
                    "name": registration.name,
                    "status": "error",
                    "health": "unhealthy",
                    "error": str(e)
                })
        
        # Calculate overall health
        healthy_agents = sum(1 for agent in agent_health if agent["health"] == "healthy")
        total_agents = len(agent_health)
        
        overall_status = "healthy"
        if healthy_agents == 0:
            overall_status = "unhealthy"
        elif healthy_agents < total_agents:
            overall_status = "degraded"
        
        return {
            "overall_status": overall_status,
            "healthy_agents": healthy_agents,
            "total_agents": total_agents,
            "agents": agent_health,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error in agents health check", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to check agent health: {str(e)}")


@router.get("/readiness")
async def readiness_check(
    hospital_system = Depends(get_hospital_system)
):
    """Readiness check - is the system ready to serve requests?"""
    try:
        if not hospital_system.running:
            return {
                "ready": False,
                "message": "Hospital system not running",
                "timestamp": datetime.now().isoformat()
            }
        
        # Check critical components
        critical_components = [
            hospital_system.communication_bus,
            hospital_system.agent_registry,
            hospital_system.coordinator,
            hospital_system.chatbot
        ]
        
        if all(component is not None for component in critical_components):
            return {
                "ready": True,
                "message": "System ready to serve requests",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "ready": False,
                "message": "Critical components not initialized",
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error("Error in readiness check", error=str(e))
        return {
            "ready": False,
            "message": f"Readiness check failed: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


@router.get("/liveness")
async def liveness_check():
    """Liveness check - is the application alive?"""
    return {
        "alive": True,
        "timestamp": datetime.now().isoformat(),
        "uptime": "N/A"  # Would calculate actual uptime in production
    }
