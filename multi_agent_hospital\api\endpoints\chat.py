"""
Chat API Endpoints

REST API endpoints for the agentic chatbot interface.
"""

from typing import Optional, Dict, Any
from datetime import datetime

import structlog
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)

router = APIRouter()


class ChatRequest(BaseModel):
    """Chat request model"""
    message: str = Field(..., description="User message", min_length=1, max_length=2000)
    session_id: Optional[str] = Field(None, description="Chat session ID")
    user_id: Optional[str] = Field("anonymous", description="User identifier")
    user_role: Optional[str] = Field("default", description="User role (doctor, nurse, admin, patient, visitor)")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Message metadata")


class ChatResponse(BaseModel):
    """Chat response model"""
    message_id: str
    session_id: str
    response: str
    actions_taken: list[str]
    follow_up_needed: bool
    follow_up_actions: list[str]
    execution_time_seconds: float
    involved_agents: list[str]
    success: bool
    metadata: Dict[str, Any]
    timestamp: str


class SessionRequest(BaseModel):
    """Session creation request"""
    user_id: str = Field(..., description="User identifier")
    user_role: str = Field("default", description="User role")
    initial_context: Optional[Dict[str, Any]] = Field(None, description="Initial session context")


class SessionResponse(BaseModel):
    """Session response model"""
    session_id: str
    user_id: str
    user_role: str
    created_at: str
    greeting_message: str


def get_chatbot(request: Request):
    """Dependency to get chatbot instance"""
    if not hasattr(request.app.state, 'chatbot'):
        raise HTTPException(status_code=503, detail="Chatbot not available")
    return request.app.state.chatbot


@router.post("/message", response_model=ChatResponse)
async def send_message(
    chat_request: ChatRequest,
    chatbot = Depends(get_chatbot)
):
    """
    Send a message to the agentic chatbot
    
    The chatbot will:
    1. Understand the user's intent
    2. Coordinate with appropriate hospital agents
    3. Execute necessary workflows
    4. Provide a comprehensive response
    """
    try:
        # Start session if not provided
        session_id = chat_request.session_id
        if not session_id:
            session_id = await chatbot.start_session(
                user_id=chat_request.user_id,
                user_role=chat_request.user_role,
                initial_context=chat_request.context
            )
        
        # Process the message
        response = await chatbot.process_message(
            session_id=session_id,
            message=chat_request.message,
            metadata=chat_request.metadata
        )
        
        return ChatResponse(
            message_id=response.message_id,
            session_id=session_id,
            response=response.response,
            actions_taken=response.actions_taken,
            follow_up_needed=response.follow_up_needed,
            follow_up_actions=response.follow_up_actions,
            execution_time_seconds=response.execution_time_seconds,
            involved_agents=response.involved_agents,
            success=response.success,
            metadata=response.metadata,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error("Error processing chat message", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to process message: {str(e)}")


@router.post("/session", response_model=SessionResponse)
async def create_session(
    session_request: SessionRequest,
    chatbot = Depends(get_chatbot)
):
    """
    Create a new chat session
    
    Sessions maintain conversation context and user preferences.
    """
    try:
        session_id = await chatbot.start_session(
            user_id=session_request.user_id,
            user_role=session_request.user_role,
            initial_context=session_request.initial_context
        )
        
        # Get greeting message
        greeting_templates = {
            "doctor": "Hello Doctor! I'm your AI assistant for hospital operations. How can I help you today?",
            "nurse": "Hi! I'm here to assist with patient care and hospital operations. What do you need help with?",
            "admin": "Welcome! I can help you with hospital administration, resource management, and operational tasks.",
            "patient": "Hello! I'm here to help answer questions about your care and hospital services.",
            "visitor": "Welcome to the hospital! I can provide information about visiting hours, directions, and general services.",
            "default": "Hello! I'm your hospital AI assistant. I can help with patient care, bed management, emergencies, and general hospital operations. How may I assist you?"
        }
        
        greeting = greeting_templates.get(session_request.user_role, greeting_templates["default"])
        
        return SessionResponse(
            session_id=session_id,
            user_id=session_request.user_id,
            user_role=session_request.user_role,
            created_at=datetime.now().isoformat(),
            greeting_message=greeting
        )
        
    except Exception as e:
        logger.error("Error creating chat session", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.get("/session/{session_id}")
async def get_session(
    session_id: str,
    chatbot = Depends(get_chatbot)
):
    """Get session information"""
    try:
        session_info = await chatbot.get_session_info(session_id)
        
        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return session_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting session info", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get session info: {str(e)}")


@router.delete("/session/{session_id}")
async def end_session(
    session_id: str,
    chatbot = Depends(get_chatbot)
):
    """End a chat session"""
    try:
        success = await chatbot.end_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {"message": "Session ended successfully", "session_id": session_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error ending session", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")


@router.get("/sessions/active")
async def get_active_sessions(
    chatbot = Depends(get_chatbot)
):
    """Get count of active chat sessions"""
    try:
        count = chatbot.get_active_sessions_count()
        
        return {
            "active_sessions": count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error getting active sessions", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get active sessions: {str(e)}")


class BroadcastRequest(BaseModel):
    """Broadcast message request"""
    message: str = Field(..., description="Message to broadcast")
    target_roles: Optional[list[str]] = Field(None, description="Target user roles (None = all)")


@router.post("/broadcast")
async def broadcast_message(
    broadcast_request: BroadcastRequest,
    chatbot = Depends(get_chatbot)
):
    """
    Broadcast a system message to active sessions
    
    Useful for emergency notifications, system announcements, etc.
    """
    try:
        count = await chatbot.broadcast_system_message(
            message=broadcast_request.message,
            target_roles=broadcast_request.target_roles
        )
        
        return {
            "message": "Broadcast sent successfully",
            "recipients": count,
            "target_roles": broadcast_request.target_roles,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Error broadcasting message", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to broadcast message: {str(e)}")


# Example usage endpoints for testing

@router.post("/examples/patient-admission")
async def example_patient_admission(
    chatbot = Depends(get_chatbot)
):
    """Example: Patient admission workflow"""
    
    example_message = "I need to admit a new patient. 45-year-old male with chest pain, emergency admission to cardiology ward."
    
    session_id = await chatbot.start_session(
        user_id="example_doctor",
        user_role="doctor"
    )
    
    response = await chatbot.process_message(
        session_id=session_id,
        message=example_message
    )
    
    return {
        "example": "Patient Admission",
        "message": example_message,
        "response": response.response,
        "actions_taken": response.actions_taken,
        "involved_agents": response.involved_agents
    }


@router.post("/examples/bed-availability")
async def example_bed_availability(
    chatbot = Depends(get_chatbot)
):
    """Example: Check bed availability"""
    
    example_message = "What beds are available in the ICU? I need one for a critical patient."
    
    session_id = await chatbot.start_session(
        user_id="example_nurse",
        user_role="nurse"
    )
    
    response = await chatbot.process_message(
        session_id=session_id,
        message=example_message
    )
    
    return {
        "example": "Bed Availability Check",
        "message": example_message,
        "response": response.response,
        "actions_taken": response.actions_taken,
        "involved_agents": response.involved_agents
    }


@router.post("/examples/emergency-response")
async def example_emergency_response(
    chatbot = Depends(get_chatbot)
):
    """Example: Emergency response coordination"""
    
    example_message = "EMERGENCY: Multiple trauma patients incoming from car accident. Need immediate response protocol."
    
    session_id = await chatbot.start_session(
        user_id="example_emergency",
        user_role="doctor"
    )
    
    response = await chatbot.process_message(
        session_id=session_id,
        message=example_message
    )
    
    return {
        "example": "Emergency Response",
        "message": example_message,
        "response": response.response,
        "actions_taken": response.actions_taken,
        "involved_agents": response.involved_agents
    }
