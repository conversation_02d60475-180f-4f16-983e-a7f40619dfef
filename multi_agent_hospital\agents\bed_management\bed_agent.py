"""
Bed Management Agent

Handles all bed-related operations including allocation, optimization,
discharge planning, and capacity management.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

import structlog
from ...core.agent_framework.base_agent import BaseHospitalAgent, AgentCapability
from ...database.repositories.bed_repository import BedRepository
from ...database.repositories.patient_repository import PatientRepository

logger = structlog.get_logger(__name__)


@dataclass
class BedAllocationRequest:
    """Request for bed allocation"""
    patient_id: str
    ward_type: str
    urgency_level: str  # low, medium, high, critical
    special_requirements: List[str]
    preferred_room_type: Optional[str] = None
    isolation_required: bool = False


@dataclass
class BedAllocationResult:
    """Result of bed allocation"""
    success: bool
    bed_id: Optional[str] = None
    room_number: Optional[str] = None
    ward: Optional[str] = None
    estimated_availability: Optional[datetime] = None
    alternative_options: List[Dict[str, Any]] = None
    message: str = ""


class BedManagementAgent(BaseHospitalAgent):
    """
    Specialized agent for bed management operations
    
    Capabilities:
    - Real-time bed availability tracking
    - Intelligent bed allocation
    - Discharge prediction and planning
    - Capacity optimization
    - Emergency bed reservation
    """
    
    def __init__(self):
        capabilities = [
            AgentCapability(
                name="get_bed_availability",
                description="Get real-time bed availability by ward and criteria",
                input_schema={
                    "type": "object",
                    "properties": {
                        "ward_type": {"type": "string"},
                        "room_type": {"type": "string", "enum": ["private", "semi-private", "ward"]},
                        "special_requirements": {"type": "array", "items": {"type": "string"}},
                        "urgency_level": {"type": "string", "enum": ["low", "medium", "high", "critical"]}
                    },
                    "required": ["ward_type"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "available_beds": {"type": "array"},
                        "total_capacity": {"type": "integer"},
                        "occupancy_rate": {"type": "number"}
                    }
                },
                estimated_duration_seconds=5
            ),
            AgentCapability(
                name="allocate_bed",
                description="Allocate optimal bed for patient based on medical needs",
                input_schema={
                    "type": "object",
                    "properties": {
                        "patient_id": {"type": "string"},
                        "ward_type": {"type": "string"},
                        "urgency_level": {"type": "string"},
                        "special_requirements": {"type": "array"},
                        "isolation_required": {"type": "boolean"}
                    },
                    "required": ["patient_id", "ward_type", "urgency_level"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "success": {"type": "boolean"},
                        "bed_id": {"type": "string"},
                        "room_number": {"type": "string"},
                        "message": {"type": "string"}
                    }
                },
                estimated_duration_seconds=10
            ),
            AgentCapability(
                name="predict_discharge",
                description="Predict patient discharge times for capacity planning",
                input_schema={
                    "type": "object",
                    "properties": {
                        "ward_type": {"type": "string"},
                        "time_horizon_hours": {"type": "integer", "default": 24}
                    }
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "predicted_discharges": {"type": "array"},
                        "confidence_scores": {"type": "array"}
                    }
                },
                estimated_duration_seconds=15
            ),
            AgentCapability(
                name="optimize_bed_utilization",
                description="Optimize bed allocation across wards for maximum efficiency",
                input_schema={
                    "type": "object",
                    "properties": {
                        "target_occupancy_rate": {"type": "number", "default": 0.85},
                        "include_transfers": {"type": "boolean", "default": True}
                    }
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "optimization_plan": {"type": "object"},
                        "expected_improvement": {"type": "number"}
                    }
                },
                estimated_duration_seconds=30
            ),
            AgentCapability(
                name="reserve_emergency_beds",
                description="Reserve beds for emergency situations",
                input_schema={
                    "type": "object",
                    "properties": {
                        "emergency_type": {"type": "string"},
                        "estimated_patients": {"type": "integer"},
                        "duration_hours": {"type": "integer"}
                    },
                    "required": ["emergency_type", "estimated_patients"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "reserved_beds": {"type": "array"},
                        "reservation_id": {"type": "string"}
                    }
                },
                estimated_duration_seconds=10
            )
        ]
        
        super().__init__(
            agent_id="bed_management_001",
            name="Bed Management Agent",
            description="Manages hospital bed allocation, optimization, and capacity planning",
            capabilities=capabilities,
            max_concurrent_tasks=10
        )
        
        # Initialize repositories
        self.bed_repository = BedRepository()
        self.patient_repository = PatientRepository()
        
        # Bed allocation algorithms
        self.allocation_strategies = {
            "critical": self._allocate_critical_patient,
            "high": self._allocate_high_priority_patient,
            "medium": self._allocate_medium_priority_patient,
            "low": self._allocate_low_priority_patient
        }
    
    async def execute_capability(self, capability_name: str, params: Dict[str, Any]) -> Any:
        """Execute a specific bed management capability"""
        
        if capability_name == "get_bed_availability":
            return await self._get_bed_availability(params)
        
        elif capability_name == "allocate_bed":
            return await self._allocate_bed(params)
        
        elif capability_name == "predict_discharge":
            return await self._predict_discharge(params)
        
        elif capability_name == "optimize_bed_utilization":
            return await self._optimize_bed_utilization(params)
        
        elif capability_name == "reserve_emergency_beds":
            return await self._reserve_emergency_beds(params)
        
        else:
            raise ValueError(f"Unknown capability: {capability_name}")
    
    async def _get_bed_availability(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Get real-time bed availability"""
        try:
            ward_type = params["ward_type"]
            room_type = params.get("room_type")
            special_requirements = params.get("special_requirements", [])
            urgency_level = params.get("urgency_level", "medium")
            
            # Query available beds
            available_beds = await self.bed_repository.get_available_beds(
                ward_type=ward_type,
                room_type=room_type,
                special_requirements=special_requirements
            )
            
            # Get capacity statistics
            total_capacity = await self.bed_repository.get_ward_capacity(ward_type)
            occupied_beds = await self.bed_repository.get_occupied_bed_count(ward_type)
            occupancy_rate = occupied_beds / total_capacity if total_capacity > 0 else 0
            
            # Format response
            bed_list = []
            for bed in available_beds:
                bed_info = {
                    "bed_id": bed.id,
                    "room_number": bed.room_number,
                    "ward": bed.ward,
                    "room_type": bed.room_type,
                    "special_features": bed.special_features,
                    "last_cleaned": bed.last_cleaned.isoformat() if bed.last_cleaned else None
                }
                bed_list.append(bed_info)
            
            return {
                "available_beds": bed_list,
                "total_capacity": total_capacity,
                "occupancy_rate": round(occupancy_rate, 2),
                "ward_type": ward_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error("Error getting bed availability", error=str(e))
            raise
    
    async def _allocate_bed(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Allocate optimal bed for patient"""
        try:
            # Parse request
            request = BedAllocationRequest(
                patient_id=params["patient_id"],
                ward_type=params["ward_type"],
                urgency_level=params["urgency_level"],
                special_requirements=params.get("special_requirements", []),
                preferred_room_type=params.get("preferred_room_type"),
                isolation_required=params.get("isolation_required", False)
            )
            
            # Get patient information
            patient = await self.patient_repository.get_patient(request.patient_id)
            if not patient:
                return {
                    "success": False,
                    "message": f"Patient {request.patient_id} not found"
                }
            
            # Use appropriate allocation strategy
            allocation_strategy = self.allocation_strategies.get(
                request.urgency_level,
                self._allocate_medium_priority_patient
            )
            
            result = await allocation_strategy(request, patient)
            
            # If successful, update bed status
            if result.success and result.bed_id:
                await self.bed_repository.assign_bed(
                    bed_id=result.bed_id,
                    patient_id=request.patient_id,
                    assigned_at=datetime.now()
                )
                
                # Notify other agents
                await self._notify_bed_assignment(result.bed_id, request.patient_id)
            
            return {
                "success": result.success,
                "bed_id": result.bed_id,
                "room_number": result.room_number,
                "ward": result.ward,
                "message": result.message,
                "alternative_options": result.alternative_options or []
            }
            
        except Exception as e:
            self.logger.error("Error allocating bed", error=str(e))
            return {
                "success": False,
                "message": f"Allocation failed: {str(e)}"
            }
    
    async def _allocate_critical_patient(
        self,
        request: BedAllocationRequest,
        patient: Any
    ) -> BedAllocationResult:
        """Allocate bed for critical patient - highest priority"""
        
        # For critical patients, we may need to transfer existing patients
        available_beds = await self.bed_repository.get_available_beds(
            ward_type=request.ward_type,
            special_requirements=request.special_requirements,
            isolation_required=request.isolation_required
        )
        
        if available_beds:
            # Take the best available bed
            best_bed = available_beds[0]  # Assuming sorted by quality/suitability
            return BedAllocationResult(
                success=True,
                bed_id=best_bed.id,
                room_number=best_bed.room_number,
                ward=best_bed.ward,
                message="Critical patient allocated to best available bed"
            )
        
        # No beds available - consider transfers
        transfer_candidates = await self.bed_repository.get_transfer_candidates(
            ward_type=request.ward_type,
            min_priority_level="low"
        )
        
        if transfer_candidates:
            # Initiate transfer process
            candidate_bed = transfer_candidates[0]
            await self._initiate_patient_transfer(candidate_bed.current_patient_id)
            
            return BedAllocationResult(
                success=True,
                bed_id=candidate_bed.id,
                room_number=candidate_bed.room_number,
                ward=candidate_bed.ward,
                estimated_availability=datetime.now() + timedelta(minutes=30),
                message="Bed will be available after patient transfer"
            )
        
        return BedAllocationResult(
            success=False,
            message="No beds available even with transfers"
        )
    
    async def _allocate_high_priority_patient(
        self,
        request: BedAllocationRequest,
        patient: Any
    ) -> BedAllocationResult:
        """Allocate bed for high priority patient"""
        
        available_beds = await self.bed_repository.get_available_beds(
            ward_type=request.ward_type,
            room_type=request.preferred_room_type,
            special_requirements=request.special_requirements,
            isolation_required=request.isolation_required
        )
        
        if available_beds:
            # Select best match based on requirements
            best_bed = self._select_best_bed(available_beds, request)
            return BedAllocationResult(
                success=True,
                bed_id=best_bed.id,
                room_number=best_bed.room_number,
                ward=best_bed.ward,
                message="High priority patient allocated to suitable bed"
            )
        
        # Look for alternatives
        alternatives = await self._find_alternative_beds(request)
        
        return BedAllocationResult(
            success=False,
            alternative_options=alternatives,
            message="No immediate beds available, alternatives provided"
        )
    
    async def _allocate_medium_priority_patient(
        self,
        request: BedAllocationRequest,
        patient: Any
    ) -> BedAllocationResult:
        """Allocate bed for medium priority patient"""
        
        available_beds = await self.bed_repository.get_available_beds(
            ward_type=request.ward_type,
            special_requirements=request.special_requirements
        )
        
        if available_beds:
            # Use standard allocation logic
            suitable_bed = self._select_suitable_bed(available_beds, request)
            return BedAllocationResult(
                success=True,
                bed_id=suitable_bed.id,
                room_number=suitable_bed.room_number,
                ward=suitable_bed.ward,
                message="Medium priority patient allocated"
            )
        
        # Predict when beds will become available
        predicted_availability = await self._predict_next_available_bed(request.ward_type)
        
        return BedAllocationResult(
            success=False,
            estimated_availability=predicted_availability,
            message=f"No beds currently available. Next bed estimated at {predicted_availability}"
        )
    
    async def _allocate_low_priority_patient(
        self,
        request: BedAllocationRequest,
        patient: Any
    ) -> BedAllocationResult:
        """Allocate bed for low priority patient"""
        
        # Low priority patients get remaining beds
        available_beds = await self.bed_repository.get_available_beds(
            ward_type=request.ward_type,
            exclude_reserved=True
        )
        
        if available_beds:
            # Take any available bed
            assigned_bed = available_beds[-1]  # Take last (presumably least desirable)
            return BedAllocationResult(
                success=True,
                bed_id=assigned_bed.id,
                room_number=assigned_bed.room_number,
                ward=assigned_bed.ward,
                message="Low priority patient allocated to available bed"
            )
        
        # Add to waiting list
        await self.bed_repository.add_to_waiting_list(
            patient_id=request.patient_id,
            ward_type=request.ward_type,
            priority=request.urgency_level
        )
        
        return BedAllocationResult(
            success=False,
            message="Added to waiting list. Will be notified when bed becomes available"
        )
    
    async def _predict_discharge(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Predict patient discharge times"""
        try:
            ward_type = params.get("ward_type")
            time_horizon_hours = params.get("time_horizon_hours", 24)
            
            # Get current patients in ward
            current_patients = await self.patient_repository.get_patients_in_ward(ward_type)
            
            predictions = []
            confidence_scores = []
            
            for patient in current_patients:
                # Simple prediction based on admission time and diagnosis
                # In a real system, this would use ML models
                prediction = await self._predict_patient_discharge(patient)
                
                if prediction["estimated_discharge"] <= datetime.now() + timedelta(hours=time_horizon_hours):
                    predictions.append({
                        "patient_id": patient.id,
                        "bed_id": patient.current_bed_id,
                        "estimated_discharge": prediction["estimated_discharge"].isoformat(),
                        "confidence": prediction["confidence"]
                    })
                    confidence_scores.append(prediction["confidence"])
            
            return {
                "predicted_discharges": predictions,
                "confidence_scores": confidence_scores,
                "average_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                "time_horizon_hours": time_horizon_hours
            }
            
        except Exception as e:
            self.logger.error("Error predicting discharges", error=str(e))
            raise
    
    async def _predict_patient_discharge(self, patient: Any) -> Dict[str, Any]:
        """Predict discharge time for a specific patient"""
        # Simplified prediction logic
        # In reality, this would use ML models with features like:
        # - Diagnosis
        # - Treatment plan
        # - Patient history
        # - Doctor notes
        # - Vital signs trends
        
        admission_time = patient.admission_time
        diagnosis = patient.primary_diagnosis
        
        # Basic rules-based prediction
        if "surgery" in diagnosis.lower():
            estimated_stay_hours = 72  # 3 days
            confidence = 0.7
        elif "emergency" in diagnosis.lower():
            estimated_stay_hours = 24  # 1 day
            confidence = 0.6
        else:
            estimated_stay_hours = 48  # 2 days
            confidence = 0.8
        
        estimated_discharge = admission_time + timedelta(hours=estimated_stay_hours)
        
        return {
            "estimated_discharge": estimated_discharge,
            "confidence": confidence
        }
    
    async def _optimize_bed_utilization(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize bed allocation across wards"""
        # This would implement sophisticated optimization algorithms
        # For now, return a placeholder
        return {
            "optimization_plan": {
                "recommended_transfers": [],
                "capacity_adjustments": [],
                "efficiency_improvements": []
            },
            "expected_improvement": 0.15,
            "implementation_time": "2 hours"
        }
    
    async def _reserve_emergency_beds(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Reserve beds for emergency situations"""
        emergency_type = params["emergency_type"]
        estimated_patients = params["estimated_patients"]
        duration_hours = params.get("duration_hours", 24)
        
        # Reserve beds based on emergency type
        reserved_beds = await self.bed_repository.reserve_emergency_beds(
            emergency_type=emergency_type,
            bed_count=estimated_patients,
            duration_hours=duration_hours
        )
        
        reservation_id = f"emergency_{emergency_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return {
            "reserved_beds": [
                {
                    "bed_id": bed.id,
                    "room_number": bed.room_number,
                    "ward": bed.ward
                }
                for bed in reserved_beds
            ],
            "reservation_id": reservation_id,
            "expires_at": (datetime.now() + timedelta(hours=duration_hours)).isoformat()
        }
    
    def _select_best_bed(self, available_beds: List[Any], request: BedAllocationRequest) -> Any:
        """Select the best bed from available options"""
        # Implement scoring algorithm based on:
        # - Room type preference
        # - Special requirements
        # - Location convenience
        # - Bed quality/features
        
        # For now, return first bed (placeholder)
        return available_beds[0]
    
    def _select_suitable_bed(self, available_beds: List[Any], request: BedAllocationRequest) -> Any:
        """Select a suitable bed from available options"""
        # Simpler selection logic than _select_best_bed
        return available_beds[0]
    
    async def _find_alternative_beds(self, request: BedAllocationRequest) -> List[Dict[str, Any]]:
        """Find alternative bed options"""
        # Look in adjacent wards, different room types, etc.
        alternatives = []
        
        # Check adjacent wards
        adjacent_wards = await self.bed_repository.get_adjacent_wards(request.ward_type)
        for ward in adjacent_wards:
            beds = await self.bed_repository.get_available_beds(ward_type=ward)
            if beds:
                alternatives.append({
                    "ward_type": ward,
                    "available_count": len(beds),
                    "transfer_required": True
                })
        
        return alternatives
    
    async def _predict_next_available_bed(self, ward_type: str) -> datetime:
        """Predict when the next bed will become available"""
        # Use discharge predictions
        discharges = await self._predict_discharge({"ward_type": ward_type, "time_horizon_hours": 48})
        
        if discharges["predicted_discharges"]:
            # Return earliest predicted discharge
            earliest = min(
                discharge["estimated_discharge"]
                for discharge in discharges["predicted_discharges"]
            )
            return datetime.fromisoformat(earliest)
        
        # Default to 24 hours if no predictions
        return datetime.now() + timedelta(hours=24)
    
    async def _initiate_patient_transfer(self, patient_id: str) -> None:
        """Initiate transfer of a patient to free up a bed"""
        # This would coordinate with other agents to arrange transfer
        self.logger.info("Initiating patient transfer", patient_id=patient_id)
        
        # Notify staff coordination agent
        # Notify transport agent
        # Update patient records
    
    async def _notify_bed_assignment(self, bed_id: str, patient_id: str) -> None:
        """Notify other agents about bed assignment"""
        # This would send notifications to relevant agents
        self.logger.info(
            "Bed assigned",
            bed_id=bed_id,
            patient_id=patient_id
        )
