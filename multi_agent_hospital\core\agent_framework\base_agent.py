"""
Base Agent Framework for Hospital Operations

This module provides the foundational classes and interfaces for all hospital agents.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
from dataclasses import dataclass, field

import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)


class AgentStatus(Enum):
    """Agent operational status"""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


class MessageType(Enum):
    """Types of messages between agents"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ALERT = "alert"
    HEARTBEAT = "heartbeat"


class Priority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AgentMessage:
    """Message structure for agent communication"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: MessageType = MessageType.REQUEST
    priority: Priority = Priority.NORMAL
    payload: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    requires_response: bool = True
    timeout_seconds: int = 30


class AgentCapability(BaseModel):
    """Defines what an agent can do"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    estimated_duration_seconds: int = 30


class AgentMetrics(BaseModel):
    """Agent performance metrics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_activity: Optional[datetime] = None


class BaseHospitalAgent(ABC):
    """
    Abstract base class for all hospital agents
    
    Provides common functionality for:
    - Message handling
    - Capability registration
    - Status management
    - Metrics tracking
    """
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        capabilities: List[AgentCapability],
        max_concurrent_tasks: int = 5
    ):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.capabilities = {cap.name: cap for cap in capabilities}
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # State management
        self.status = AgentStatus.IDLE
        self.active_tasks: Set[str] = set()
        self.metrics = AgentMetrics()
        
        # Communication
        self.message_handlers: Dict[MessageType, callable] = {
            MessageType.REQUEST: self._handle_request,
            MessageType.NOTIFICATION: self._handle_notification,
            MessageType.ALERT: self._handle_alert,
            MessageType.HEARTBEAT: self._handle_heartbeat,
        }
        
        # Logging
        self.logger = structlog.get_logger(self.name)
        
        self.logger.info(
            "Agent initialized",
            agent_id=self.agent_id,
            capabilities=list(self.capabilities.keys())
        )
    
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """
        Main entry point for processing incoming messages
        """
        self.logger.debug(
            "Processing message",
            message_id=message.id,
            message_type=message.message_type.value,
            sender=message.sender_id
        )
        
        try:
            # Update metrics
            self.metrics.total_requests += 1
            self.metrics.last_activity = datetime.now()
            
            # Route to appropriate handler
            handler = self.message_handlers.get(message.message_type)
            if not handler:
                raise ValueError(f"No handler for message type: {message.message_type}")
            
            # Process with timeout
            response = await asyncio.wait_for(
                handler(message),
                timeout=message.timeout_seconds
            )
            
            # Update success metrics
            self.metrics.successful_requests += 1
            
            return response
            
        except asyncio.TimeoutError:
            self.logger.error("Message processing timeout", message_id=message.id)
            self.metrics.failed_requests += 1
            return self._create_error_response(message, "Processing timeout")
            
        except Exception as e:
            self.logger.error(
                "Error processing message",
                message_id=message.id,
                error=str(e)
            )
            self.metrics.failed_requests += 1
            return self._create_error_response(message, str(e))
    
    async def _handle_request(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming requests"""
        capability_name = message.payload.get("capability")
        
        if not capability_name:
            return self._create_error_response(message, "No capability specified")
        
        if capability_name not in self.capabilities:
            return self._create_error_response(
                message, f"Capability '{capability_name}' not supported"
            )
        
        # Check if we can handle more tasks
        if len(self.active_tasks) >= self.max_concurrent_tasks:
            return self._create_error_response(message, "Agent at capacity")
        
        # Add to active tasks
        task_id = str(uuid.uuid4())
        self.active_tasks.add(task_id)
        self.status = AgentStatus.BUSY
        
        try:
            # Execute the capability
            result = await self.execute_capability(capability_name, message.payload)
            
            # Create response
            response = AgentMessage(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.RESPONSE,
                payload={"result": result, "success": True},
                correlation_id=message.id
            )
            
            return response
            
        finally:
            # Clean up
            self.active_tasks.discard(task_id)
            if not self.active_tasks:
                self.status = AgentStatus.IDLE
    
    async def _handle_notification(self, message: AgentMessage) -> None:
        """Handle notifications (no response required)"""
        await self.handle_notification(message.payload)
    
    async def _handle_alert(self, message: AgentMessage) -> None:
        """Handle alerts (high priority notifications)"""
        await self.handle_alert(message.payload)
    
    async def _handle_heartbeat(self, message: AgentMessage) -> AgentMessage:
        """Handle heartbeat requests"""
        return AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.RESPONSE,
            payload={
                "status": self.status.value,
                "active_tasks": len(self.active_tasks),
                "metrics": self.metrics.dict()
            },
            correlation_id=message.id
        )
    
    def _create_error_response(self, original_message: AgentMessage, error: str) -> AgentMessage:
        """Create an error response message"""
        return AgentMessage(
            sender_id=self.agent_id,
            receiver_id=original_message.sender_id,
            message_type=MessageType.RESPONSE,
            payload={"error": error, "success": False},
            correlation_id=original_message.id
        )
    
    @abstractmethod
    async def execute_capability(self, capability_name: str, params: Dict[str, Any]) -> Any:
        """
        Execute a specific capability
        
        Args:
            capability_name: Name of the capability to execute
            params: Parameters for the capability
            
        Returns:
            Result of the capability execution
        """
        pass
    
    async def handle_notification(self, payload: Dict[str, Any]) -> None:
        """Handle incoming notifications (override in subclasses)"""
        self.logger.info("Received notification", payload=payload)
    
    async def handle_alert(self, payload: Dict[str, Any]) -> None:
        """Handle incoming alerts (override in subclasses)"""
        self.logger.warning("Received alert", payload=payload)
    
    def get_capabilities(self) -> List[AgentCapability]:
        """Get list of agent capabilities"""
        return list(self.capabilities.values())
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "active_tasks": len(self.active_tasks),
            "capabilities": list(self.capabilities.keys()),
            "metrics": self.metrics.dict()
        }
