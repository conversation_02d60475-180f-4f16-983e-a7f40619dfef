"""
Workflow Engine

Orchestrates complex multi-agent workflows for hospital operations.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import structlog

logger = structlog.get_logger(__name__)


class WorkflowStatus(Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowStep:
    """Individual step in a workflow"""
    step_id: str
    agent_id: str
    capability: str
    parameters: Dict[str, Any]
    depends_on: List[str]  # Step IDs this step depends on
    timeout_seconds: int = 30


@dataclass
class Workflow:
    """Workflow definition"""
    workflow_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    created_at: datetime
    status: WorkflowStatus = WorkflowStatus.PENDING


class WorkflowEngine:
    """Engine for executing multi-agent workflows"""
    
    def __init__(self, agent_registry, communication_bus):
        self.agent_registry = agent_registry
        self.communication_bus = communication_bus
        self.active_workflows: Dict[str, Workflow] = {}
        self.workflow_results: Dict[str, Dict[str, Any]] = {}
    
    async def execute_workflow(self, workflow: Workflow) -> Dict[str, Any]:
        """Execute a workflow"""
        
        workflow.status = WorkflowStatus.RUNNING
        self.active_workflows[workflow.workflow_id] = workflow
        self.workflow_results[workflow.workflow_id] = {}
        
        try:
            # Execute steps in dependency order
            completed_steps = set()
            
            while len(completed_steps) < len(workflow.steps):
                # Find steps that can be executed (dependencies met)
                ready_steps = [
                    step for step in workflow.steps
                    if step.step_id not in completed_steps
                    and all(dep in completed_steps for dep in step.depends_on)
                ]
                
                if not ready_steps:
                    raise Exception("Workflow deadlock: no steps can be executed")
                
                # Execute ready steps in parallel
                tasks = []
                for step in ready_steps:
                    task = asyncio.create_task(self._execute_step(workflow.workflow_id, step))
                    tasks.append((step.step_id, task))
                
                # Wait for all tasks to complete
                for step_id, task in tasks:
                    try:
                        result = await task
                        self.workflow_results[workflow.workflow_id][step_id] = result
                        completed_steps.add(step_id)
                    except Exception as e:
                        logger.error("Step failed", workflow_id=workflow.workflow_id, step_id=step_id, error=str(e))
                        workflow.status = WorkflowStatus.FAILED
                        raise
            
            workflow.status = WorkflowStatus.COMPLETED
            
            return {
                "workflow_id": workflow.workflow_id,
                "status": workflow.status.value,
                "results": self.workflow_results[workflow.workflow_id]
            }
            
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            logger.error("Workflow failed", workflow_id=workflow.workflow_id, error=str(e))
            raise
        
        finally:
            # Cleanup
            if workflow.workflow_id in self.active_workflows:
                del self.active_workflows[workflow.workflow_id]
    
    async def _execute_step(self, workflow_id: str, step: WorkflowStep) -> Any:
        """Execute a single workflow step"""
        
        logger.info(
            "Executing workflow step",
            workflow_id=workflow_id,
            step_id=step.step_id,
            agent_id=step.agent_id,
            capability=step.capability
        )
        
        # Find the agent
        agent_registration = self.agent_registry.get_agent(step.agent_id)
        if not agent_registration:
            raise Exception(f"Agent {step.agent_id} not found")
        
        # Send message to agent
        from ..agent_framework.base_agent import AgentMessage, MessageType
        
        message = AgentMessage(
            sender_id="workflow_engine",
            receiver_id=step.agent_id,
            message_type=MessageType.REQUEST,
            payload={
                "capability": step.capability,
                **step.parameters
            },
            timeout_seconds=step.timeout_seconds
        )
        
        response = await self.communication_bus.send_message(message, timeout_seconds=step.timeout_seconds)
        
        if not response or not response.payload.get("success"):
            raise Exception(f"Step {step.step_id} failed: {response.payload.get('error') if response else 'No response'}")
        
        return response.payload.get("result")
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a workflow"""
        
        workflow = self.active_workflows.get(workflow_id)
        if not workflow:
            return None
        
        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "created_at": workflow.created_at.isoformat(),
            "steps_total": len(workflow.steps),
            "steps_completed": len(self.workflow_results.get(workflow_id, {}))
        }
