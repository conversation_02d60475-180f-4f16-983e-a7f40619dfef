# Database Configuration
DATABASE_URL=postgresql+asyncpg://hospital_user:hospital_pass@localhost:5432/hospital_db
REDIS_URL=redis://localhost:6379/0

# Message Queue
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Google Gemini API
GOOGLE_API_KEY=your_gemini_api_key_here

# ChromaDB
CHROMA_DB_PATH=./data/chroma_db

# Application Settings
APP_NAME=Multi-Agent Hospital System
APP_VERSION=1.0.0
DEBUG=True
LOG_LEVEL=INFO

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Monitoring
PROMETHEUS_PORT=8090

# Agent Configuration
MAX_CONCURRENT_AGENTS=10
AGENT_TIMEOUT_SECONDS=30
WORKFLOW_TIMEOUT_SECONDS=120
