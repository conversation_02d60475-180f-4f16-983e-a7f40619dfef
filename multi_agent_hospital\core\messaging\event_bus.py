"""
Event Bus

Simple event broadcasting system for agent coordination.
"""

import asyncio
from typing import Dict, List, Callable, Any
from datetime import datetime

import structlog

logger = structlog.get_logger(__name__)


class EventBus:
    """Simple event bus for broadcasting events between agents"""
    
    def __init__(self):
        self.subscribers: Dict[str, List[Callable]] = {}
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to an event type"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        
        self.subscribers[event_type].append(callback)
        logger.info("Subscribed to event", event_type=event_type)
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from an event type"""
        if event_type in self.subscribers:
            try:
                self.subscribers[event_type].remove(callback)
                logger.info("Unsubscribed from event", event_type=event_type)
            except ValueError:
                pass
    
    async def publish(self, event_type: str, data: Any):
        """Publish an event to all subscribers"""
        if event_type not in self.subscribers:
            return
        
        event_data = {
            "type": event_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Notify all subscribers
        for callback in self.subscribers[event_type]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_data)
                else:
                    callback(event_data)
            except Exception as e:
                logger.error("Error in event callback", event_type=event_type, error=str(e))
