"""
Central Coordinator Agent

Master agent that orchestrates all hospital operations by coordinating
between specialized agents and managing complex workflows.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import structlog
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

from ..agent_framework.base_agent import BaseHospitalAgent, AgentCapability, AgentMessage, MessageType, Priority
from ..agent_framework.agent_registry import AgentRegistry
from ..agent_framework.communication_bus import AgentCommunicationBus
from ..workflow.workflow_engine import WorkflowEngine
from ...config.settings import settings

logger = structlog.get_logger(__name__)


class RequestType(Enum):
    """Types of requests the coordinator can handle"""
    PATIENT_ADMISSION = "patient_admission"
    BED_ALLOCATION = "bed_allocation"
    EMERGENCY_RESPONSE = "emergency_response"
    TREATMENT_COORDINATION = "treatment_coordination"
    DISCHARGE_PLANNING = "discharge_planning"
    RESOURCE_MANAGEMENT = "resource_management"
    GENERAL_INQUIRY = "general_inquiry"


@dataclass
class CoordinationRequest:
    """Request for coordination between agents"""
    request_id: str
    request_type: RequestType
    user_message: str
    context: Dict[str, Any]
    priority: Priority
    requester_id: str
    timestamp: datetime


@dataclass
class CoordinationResult:
    """Result of coordination"""
    request_id: str
    success: bool
    result: Dict[str, Any]
    involved_agents: List[str]
    execution_time_seconds: float
    message: str
    follow_up_actions: List[str]


class CentralCoordinatorAgent(BaseHospitalAgent):
    """
    Central coordinator that orchestrates all hospital operations
    
    Capabilities:
    - Natural language understanding and intent classification
    - Multi-agent workflow orchestration
    - Context management and conversation handling
    - Response synthesis and formatting
    - Emergency escalation and priority management
    """
    
    def __init__(self, agent_registry: AgentRegistry, communication_bus: AgentCommunicationBus):
        capabilities = [
            AgentCapability(
                name="coordinate_patient_admission",
                description="Coordinate complete patient admission workflow across multiple agents",
                input_schema={
                    "type": "object",
                    "properties": {
                        "patient_data": {"type": "object"},
                        "admission_type": {"type": "string"},
                        "urgency_level": {"type": "string"},
                        "special_requirements": {"type": "array"}
                    },
                    "required": ["patient_data", "admission_type"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "admission_id": {"type": "string"},
                        "assigned_bed": {"type": "string"},
                        "care_team": {"type": "array"},
                        "next_steps": {"type": "array"}
                    }
                },
                estimated_duration_seconds=60
            ),
            AgentCapability(
                name="handle_emergency_response",
                description="Coordinate emergency response across all relevant agents",
                input_schema={
                    "type": "object",
                    "properties": {
                        "emergency_type": {"type": "string"},
                        "severity": {"type": "string"},
                        "patient_count": {"type": "integer"},
                        "special_requirements": {"type": "array"}
                    },
                    "required": ["emergency_type", "severity"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "response_id": {"type": "string"},
                        "mobilized_resources": {"type": "array"},
                        "estimated_response_time": {"type": "string"}
                    }
                },
                estimated_duration_seconds=30
            ),
            AgentCapability(
                name="process_natural_language_request",
                description="Process natural language requests and coordinate appropriate agents",
                input_schema={
                    "type": "object",
                    "properties": {
                        "user_message": {"type": "string"},
                        "context": {"type": "object"},
                        "user_role": {"type": "string"}
                    },
                    "required": ["user_message"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "response": {"type": "string"},
                        "actions_taken": {"type": "array"},
                        "follow_up_needed": {"type": "boolean"}
                    }
                },
                estimated_duration_seconds=45
            ),
            AgentCapability(
                name="optimize_hospital_operations",
                description="Analyze and optimize hospital operations across all departments",
                input_schema={
                    "type": "object",
                    "properties": {
                        "optimization_scope": {"type": "string"},
                        "time_horizon": {"type": "string"},
                        "constraints": {"type": "object"}
                    }
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "optimization_plan": {"type": "object"},
                        "expected_improvements": {"type": "array"},
                        "implementation_steps": {"type": "array"}
                    }
                },
                estimated_duration_seconds=120
            )
        ]
        
        super().__init__(
            agent_id="central_coordinator_001",
            name="Central Coordinator Agent",
            description="Master orchestrator for all hospital operations and agent coordination",
            capabilities=capabilities,
            max_concurrent_tasks=20
        )
        
        # Core components
        self.agent_registry = agent_registry
        self.communication_bus = communication_bus
        self.workflow_engine = WorkflowEngine(agent_registry, communication_bus)
        
        # LLM for natural language processing
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-pro",
            google_api_key=settings.google_api_key,
            temperature=0.1
        )
        
        # Intent classification patterns
        self.intent_patterns = {
            RequestType.PATIENT_ADMISSION: [
                "admit", "admission", "new patient", "check in", "register patient"
            ],
            RequestType.BED_ALLOCATION: [
                "bed", "room", "assign bed", "bed availability", "find bed"
            ],
            RequestType.EMERGENCY_RESPONSE: [
                "emergency", "urgent", "critical", "trauma", "code", "alert"
            ],
            RequestType.TREATMENT_COORDINATION: [
                "treatment", "therapy", "procedure", "surgery", "coordinate care"
            ],
            RequestType.DISCHARGE_PLANNING: [
                "discharge", "release", "going home", "discharge planning"
            ],
            RequestType.RESOURCE_MANAGEMENT: [
                "equipment", "supplies", "resources", "inventory", "maintenance"
            ]
        }
        
        # Conversation context
        self.conversation_contexts: Dict[str, Dict[str, Any]] = {}
    
    async def execute_capability(self, capability_name: str, params: Dict[str, Any]) -> Any:
        """Execute a specific coordination capability"""
        
        if capability_name == "coordinate_patient_admission":
            return await self._coordinate_patient_admission(params)
        
        elif capability_name == "handle_emergency_response":
            return await self._handle_emergency_response(params)
        
        elif capability_name == "process_natural_language_request":
            return await self._process_natural_language_request(params)
        
        elif capability_name == "optimize_hospital_operations":
            return await self._optimize_hospital_operations(params)
        
        else:
            raise ValueError(f"Unknown capability: {capability_name}")
    
    async def process_user_message(
        self,
        user_message: str,
        context: Optional[Dict[str, Any]] = None,
        user_id: str = "anonymous"
    ) -> Dict[str, Any]:
        """
        Main entry point for processing user messages
        
        This is the primary interface for the agentic chatbot
        """
        try:
            start_time = datetime.now()
            
            # Generate request ID
            request_id = str(uuid.uuid4())
            
            # Classify intent and extract parameters
            intent_result = await self._classify_intent_and_extract_params(user_message, context)
            
            # Create coordination request
            coordination_request = CoordinationRequest(
                request_id=request_id,
                request_type=intent_result["request_type"],
                user_message=user_message,
                context=context or {},
                priority=intent_result["priority"],
                requester_id=user_id,
                timestamp=start_time
            )
            
            # Execute coordination
            result = await self._execute_coordination(coordination_request, intent_result["parameters"])
            
            # Generate natural language response
            response = await self._generate_response(coordination_request, result)
            
            # Update conversation context
            self._update_conversation_context(user_id, user_message, response, result)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "request_id": request_id,
                "response": response,
                "actions_taken": result.result.get("actions_taken", []),
                "follow_up_needed": len(result.follow_up_actions) > 0,
                "follow_up_actions": result.follow_up_actions,
                "execution_time_seconds": execution_time,
                "involved_agents": result.involved_agents,
                "success": result.success
            }
            
        except Exception as e:
            self.logger.error("Error processing user message", error=str(e), user_message=user_message)
            return {
                "request_id": str(uuid.uuid4()),
                "response": f"I apologize, but I encountered an error processing your request: {str(e)}. Please try again or contact support if the issue persists.",
                "actions_taken": [],
                "follow_up_needed": False,
                "follow_up_actions": [],
                "execution_time_seconds": 0,
                "involved_agents": [],
                "success": False,
                "error": str(e)
            }
    
    async def _classify_intent_and_extract_params(
        self,
        user_message: str,
        context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Classify user intent and extract parameters using LLM"""
        
        # First, try pattern matching for quick classification
        request_type = self._pattern_match_intent(user_message)
        
        # Use LLM for more sophisticated understanding
        system_prompt = """You are a hospital operations AI assistant. Analyze the user's message and classify their intent.
        
Available request types:
- patient_admission: Admitting new patients
- bed_allocation: Finding or assigning beds
- emergency_response: Handling emergencies
- treatment_coordination: Coordinating patient care
- discharge_planning: Planning patient discharge
- resource_management: Managing equipment/supplies
- general_inquiry: General questions or information requests

Determine:
1. The request type
2. Priority level (low, normal, high, critical)
3. Key parameters from the message

Respond in JSON format with: request_type, priority, parameters, confidence"""
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"User message: {user_message}\nContext: {context or {}}")
        ]
        
        try:
            llm_response = await self.llm.ainvoke(messages)
            
            # Parse LLM response (simplified - would use structured output in production)
            # For now, use pattern matching result with LLM enhancement
            
            priority = self._determine_priority(user_message)
            parameters = self._extract_parameters(user_message, request_type)
            
            return {
                "request_type": request_type,
                "priority": priority,
                "parameters": parameters,
                "confidence": 0.8  # Would be determined by LLM
            }
            
        except Exception as e:
            self.logger.warning("LLM classification failed, using pattern matching", error=str(e))
            
            return {
                "request_type": request_type,
                "priority": Priority.NORMAL,
                "parameters": self._extract_parameters(user_message, request_type),
                "confidence": 0.6
            }
    
    def _pattern_match_intent(self, user_message: str) -> RequestType:
        """Pattern match user message to determine intent"""
        message_lower = user_message.lower()
        
        # Check for emergency keywords first
        emergency_keywords = ["emergency", "urgent", "critical", "code", "trauma", "help"]
        if any(keyword in message_lower for keyword in emergency_keywords):
            return RequestType.EMERGENCY_RESPONSE
        
        # Check other patterns
        for request_type, patterns in self.intent_patterns.items():
            if any(pattern in message_lower for pattern in patterns):
                return request_type
        
        return RequestType.GENERAL_INQUIRY
    
    def _determine_priority(self, user_message: str) -> Priority:
        """Determine priority based on message content"""
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["emergency", "urgent", "critical", "asap"]):
            return Priority.CRITICAL
        elif any(word in message_lower for word in ["high priority", "important", "soon"]):
            return Priority.HIGH
        elif any(word in message_lower for word in ["low priority", "when possible", "routine"]):
            return Priority.LOW
        else:
            return Priority.NORMAL
    
    def _extract_parameters(self, user_message: str, request_type: RequestType) -> Dict[str, Any]:
        """Extract parameters from user message based on request type"""
        parameters = {}
        message_lower = user_message.lower()
        
        if request_type == RequestType.PATIENT_ADMISSION:
            # Extract patient-related information
            if "emergency" in message_lower:
                parameters["admission_type"] = "emergency"
            elif "elective" in message_lower:
                parameters["admission_type"] = "elective"
            else:
                parameters["admission_type"] = "standard"
        
        elif request_type == RequestType.BED_ALLOCATION:
            # Extract bed preferences
            if "icu" in message_lower:
                parameters["ward_type"] = "icu"
            elif "surgery" in message_lower:
                parameters["ward_type"] = "surgical"
            elif "pediatric" in message_lower:
                parameters["ward_type"] = "pediatrics"
            else:
                parameters["ward_type"] = "general"
        
        elif request_type == RequestType.EMERGENCY_RESPONSE:
            # Extract emergency details
            if "trauma" in message_lower:
                parameters["emergency_type"] = "trauma"
            elif "cardiac" in message_lower or "heart" in message_lower:
                parameters["emergency_type"] = "cardiac"
            else:
                parameters["emergency_type"] = "general"
        
        return parameters

    async def _execute_coordination(
        self,
        request: CoordinationRequest,
        parameters: Dict[str, Any]
    ) -> CoordinationResult:
        """Execute coordination based on request type"""

        start_time = datetime.now()

        try:
            if request.request_type == RequestType.PATIENT_ADMISSION:
                result = await self._coordinate_patient_admission({
                    **parameters,
                    "user_message": request.user_message,
                    "context": request.context
                })

            elif request.request_type == RequestType.BED_ALLOCATION:
                result = await self._coordinate_bed_allocation(parameters)

            elif request.request_type == RequestType.EMERGENCY_RESPONSE:
                result = await self._handle_emergency_response({
                    **parameters,
                    "user_message": request.user_message
                })

            elif request.request_type == RequestType.TREATMENT_COORDINATION:
                result = await self._coordinate_treatment(parameters)

            elif request.request_type == RequestType.DISCHARGE_PLANNING:
                result = await self._coordinate_discharge_planning(parameters)

            elif request.request_type == RequestType.RESOURCE_MANAGEMENT:
                result = await self._coordinate_resource_management(parameters)

            else:  # GENERAL_INQUIRY
                result = await self._handle_general_inquiry(request.user_message, request.context)

            execution_time = (datetime.now() - start_time).total_seconds()

            return CoordinationResult(
                request_id=request.request_id,
                success=True,
                result=result,
                involved_agents=result.get("involved_agents", []),
                execution_time_seconds=execution_time,
                message="Coordination completed successfully",
                follow_up_actions=result.get("follow_up_actions", [])
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            return CoordinationResult(
                request_id=request.request_id,
                success=False,
                result={"error": str(e)},
                involved_agents=[],
                execution_time_seconds=execution_time,
                message=f"Coordination failed: {str(e)}",
                follow_up_actions=["Contact system administrator"]
            )

    async def _coordinate_patient_admission(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate complete patient admission workflow"""

        involved_agents = []
        actions_taken = []

        try:
            # Step 1: Create patient record (Patient Care Agent)
            patient_agent = self.agent_registry.find_least_loaded_agent("admit_patient")
            if not patient_agent:
                raise Exception("No patient care agent available")

            involved_agents.append(patient_agent)

            # Prepare patient data from user message and context
            patient_data = self._extract_patient_data_from_message(params)

            admission_message = AgentMessage(
                sender_id=self.agent_id,
                receiver_id=patient_agent,
                message_type=MessageType.REQUEST,
                payload={
                    "capability": "admit_patient",
                    "patient_data": patient_data,
                    "admission_type": params.get("admission_type", "standard"),
                    "primary_diagnosis": params.get("primary_diagnosis", "To be determined"),
                    "urgency_level": params.get("urgency_level", "medium")
                }
            )

            admission_response = await self.communication_bus.send_message(admission_message)
            if not admission_response or not admission_response.payload.get("success"):
                raise Exception("Patient admission failed")

            admission_result = admission_response.payload["result"]
            actions_taken.append("Patient admitted and care plan created")

            # Step 2: Allocate bed (Bed Management Agent)
            bed_agent = self.agent_registry.find_least_loaded_agent("allocate_bed")
            if bed_agent:
                involved_agents.append(bed_agent)

                bed_message = AgentMessage(
                    sender_id=self.agent_id,
                    receiver_id=bed_agent,
                    message_type=MessageType.REQUEST,
                    payload={
                        "capability": "allocate_bed",
                        "patient_id": admission_result["patient_id"],
                        "ward_type": params.get("ward_type", "general"),
                        "urgency_level": params.get("urgency_level", "medium"),
                        "special_requirements": params.get("special_requirements", [])
                    }
                )

                bed_response = await self.communication_bus.send_message(bed_message)
                if bed_response and bed_response.payload.get("success"):
                    bed_result = bed_response.payload["result"]
                    admission_result["assigned_bed"] = bed_result.get("bed_id")
                    admission_result["room_number"] = bed_result.get("room_number")
                    actions_taken.append(f"Bed allocated: {bed_result.get('room_number')}")

            # Step 3: Coordinate with staff (Staff Coordination Agent)
            staff_agent = self.agent_registry.find_least_loaded_agent("assign_care_team")
            if staff_agent:
                involved_agents.append(staff_agent)
                actions_taken.append("Care team assigned")

            # Step 4: Notify relevant departments
            await self._notify_admission_departments(admission_result)
            actions_taken.append("Departments notified")

            return {
                "admission_id": admission_result.get("admission_id"),
                "patient_id": admission_result.get("patient_id"),
                "assigned_bed": admission_result.get("assigned_bed"),
                "room_number": admission_result.get("room_number"),
                "care_plan_id": admission_result.get("care_plan_id"),
                "care_team": admission_result.get("care_team", []),
                "next_steps": admission_result.get("next_steps", []),
                "involved_agents": involved_agents,
                "actions_taken": actions_taken,
                "follow_up_actions": [
                    "Monitor patient status",
                    "Complete initial assessment",
                    "Begin treatment plan"
                ]
            }

        except Exception as e:
            self.logger.error("Error coordinating patient admission", error=str(e))
            raise

    async def _coordinate_bed_allocation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate bed allocation request"""

        bed_agent = self.agent_registry.find_least_loaded_agent("get_bed_availability")
        if not bed_agent:
            raise Exception("No bed management agent available")

        # Get bed availability
        availability_message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=bed_agent,
            message_type=MessageType.REQUEST,
            payload={
                "capability": "get_bed_availability",
                "ward_type": params.get("ward_type", "general"),
                "room_type": params.get("room_type"),
                "special_requirements": params.get("special_requirements", []),
                "urgency_level": params.get("urgency_level", "medium")
            }
        )

        response = await self.communication_bus.send_message(availability_message)
        if not response or not response.payload.get("success"):
            raise Exception("Failed to get bed availability")

        availability_result = response.payload["result"]

        return {
            "available_beds": availability_result["available_beds"],
            "total_capacity": availability_result["total_capacity"],
            "occupancy_rate": availability_result["occupancy_rate"],
            "ward_type": availability_result["ward_type"],
            "involved_agents": [bed_agent],
            "actions_taken": ["Retrieved bed availability information"],
            "follow_up_actions": []
        }

    async def _handle_emergency_response(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle emergency response coordination"""

        involved_agents = []
        actions_taken = []

        emergency_type = params.get("emergency_type", "general")
        severity = params.get("severity", "high")

        # Step 1: Alert emergency response agent
        emergency_agent = self.agent_registry.find_least_loaded_agent("handle_emergency")
        if emergency_agent:
            involved_agents.append(emergency_agent)
            actions_taken.append("Emergency response protocol activated")

        # Step 2: Reserve emergency beds
        bed_agent = self.agent_registry.find_least_loaded_agent("reserve_emergency_beds")
        if bed_agent:
            involved_agents.append(bed_agent)

            bed_message = AgentMessage(
                sender_id=self.agent_id,
                receiver_id=bed_agent,
                message_type=MessageType.REQUEST,
                payload={
                    "capability": "reserve_emergency_beds",
                    "emergency_type": emergency_type,
                    "estimated_patients": params.get("patient_count", 1),
                    "duration_hours": 24
                }
            )

            bed_response = await self.communication_bus.send_message(bed_message)
            if bed_response and bed_response.payload.get("success"):
                actions_taken.append("Emergency beds reserved")

        # Step 3: Mobilize staff
        staff_agent = self.agent_registry.find_least_loaded_agent("mobilize_emergency_staff")
        if staff_agent:
            involved_agents.append(staff_agent)
            actions_taken.append("Emergency staff mobilized")

        # Step 4: Prepare resources
        resource_agent = self.agent_registry.find_least_loaded_agent("prepare_emergency_resources")
        if resource_agent:
            involved_agents.append(resource_agent)
            actions_taken.append("Emergency resources prepared")

        # Step 5: Broadcast emergency alert
        await self.communication_bus.broadcast_event(
            "emergency_alert",
            {
                "emergency_type": emergency_type,
                "severity": severity,
                "timestamp": datetime.now().isoformat(),
                "response_id": str(uuid.uuid4())
            }
        )
        actions_taken.append("Emergency alert broadcasted")

        return {
            "response_id": str(uuid.uuid4()),
            "emergency_type": emergency_type,
            "severity": severity,
            "mobilized_resources": actions_taken,
            "estimated_response_time": "5-10 minutes",
            "involved_agents": involved_agents,
            "actions_taken": actions_taken,
            "follow_up_actions": [
                "Monitor emergency response",
                "Coordinate with external services",
                "Prepare for patient arrival"
            ]
        }

    async def _coordinate_treatment(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate treatment activities"""

        patient_agent = self.agent_registry.find_least_loaded_agent("coordinate_treatment")
        if not patient_agent:
            raise Exception("No patient care agent available")

        treatment_message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=patient_agent,
            message_type=MessageType.REQUEST,
            payload={
                "capability": "coordinate_treatment",
                **params
            }
        )

        response = await self.communication_bus.send_message(treatment_message)
        if not response or not response.payload.get("success"):
            raise Exception("Treatment coordination failed")

        result = response.payload["result"]

        return {
            **result,
            "involved_agents": [patient_agent],
            "actions_taken": ["Treatment coordination completed"],
            "follow_up_actions": ["Monitor treatment progress"]
        }

    async def _coordinate_discharge_planning(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate discharge planning"""

        patient_agent = self.agent_registry.find_least_loaded_agent("plan_discharge")
        if not patient_agent:
            raise Exception("No patient care agent available")

        discharge_message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=patient_agent,
            message_type=MessageType.REQUEST,
            payload={
                "capability": "plan_discharge",
                **params
            }
        )

        response = await self.communication_bus.send_message(discharge_message)
        if not response or not response.payload.get("success"):
            raise Exception("Discharge planning failed")

        result = response.payload["result"]

        return {
            **result,
            "involved_agents": [patient_agent],
            "actions_taken": ["Discharge plan created"],
            "follow_up_actions": ["Coordinate discharge activities"]
        }

    async def _coordinate_resource_management(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate resource management"""

        resource_agent = self.agent_registry.find_least_loaded_agent("manage_resources")
        if not resource_agent:
            return {
                "message": "Resource management agent not available",
                "involved_agents": [],
                "actions_taken": [],
                "follow_up_actions": ["Contact resource management manually"]
            }

        return {
            "message": "Resource management request processed",
            "involved_agents": [resource_agent],
            "actions_taken": ["Resource request forwarded"],
            "follow_up_actions": []
        }

    async def _handle_general_inquiry(self, user_message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general inquiries using LLM"""

        # Use LLM to generate response for general inquiries
        system_prompt = """You are a helpful hospital operations assistant. Provide informative and helpful responses to general inquiries about hospital operations, procedures, and services. Be professional, empathetic, and accurate."""

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"User inquiry: {user_message}\nContext: {context}")
        ]

        try:
            llm_response = await self.llm.ainvoke(messages)
            response_text = llm_response.content

            return {
                "response": response_text,
                "involved_agents": [self.agent_id],
                "actions_taken": ["Generated informational response"],
                "follow_up_actions": []
            }

        except Exception as e:
            return {
                "response": "I apologize, but I'm unable to process your inquiry at the moment. Please contact hospital staff for assistance.",
                "involved_agents": [],
                "actions_taken": [],
                "follow_up_actions": ["Contact hospital staff"],
                "error": str(e)
            }

    async def _generate_response(
        self,
        request: CoordinationRequest,
        result: CoordinationResult
    ) -> str:
        """Generate natural language response"""

        if not result.success:
            return f"I apologize, but I encountered an issue processing your request: {result.message}. Please try again or contact support."

        # Generate contextual response based on request type
        if request.request_type == RequestType.PATIENT_ADMISSION:
            return self._generate_admission_response(result.result)

        elif request.request_type == RequestType.BED_ALLOCATION:
            return self._generate_bed_allocation_response(result.result)

        elif request.request_type == RequestType.EMERGENCY_RESPONSE:
            return self._generate_emergency_response(result.result)

        elif request.request_type == RequestType.GENERAL_INQUIRY:
            return result.result.get("response", "I've processed your inquiry.")

        else:
            return f"I've successfully processed your {request.request_type.value.replace('_', ' ')} request. {result.message}"

    def _generate_admission_response(self, result: Dict[str, Any]) -> str:
        """Generate response for patient admission"""

        patient_id = result.get("patient_id", "Unknown")
        assigned_bed = result.get("room_number", result.get("assigned_bed", "TBD"))

        response = f"✅ Patient admission completed successfully!\n\n"
        response += f"📋 **Patient ID:** {patient_id}\n"

        if assigned_bed and assigned_bed != "TBD":
            response += f"🛏️ **Assigned Bed:** {assigned_bed}\n"
        else:
            response += f"🛏️ **Bed Assignment:** In progress\n"

        if result.get("care_plan_id"):
            response += f"📝 **Care Plan:** Created (ID: {result['care_plan_id']})\n"

        if result.get("next_steps"):
            response += f"\n**Next Steps:**\n"
            for step in result["next_steps"]:
                response += f"• {step}\n"

        return response

    def _generate_bed_allocation_response(self, result: Dict[str, Any]) -> str:
        """Generate response for bed allocation"""

        available_beds = result.get("available_beds", [])
        occupancy_rate = result.get("occupancy_rate", 0)
        ward_type = result.get("ward_type", "general")

        response = f"🛏️ **Bed Availability - {ward_type.title()} Ward**\n\n"
        response += f"📊 **Occupancy Rate:** {occupancy_rate:.1%}\n"
        response += f"🟢 **Available Beds:** {len(available_beds)}\n\n"

        if available_beds:
            response += "**Available Options:**\n"
            for bed in available_beds[:5]:  # Show first 5
                response += f"• Room {bed.get('room_number', 'N/A')} - {bed.get('room_type', 'Standard')}\n"

            if len(available_beds) > 5:
                response += f"• ... and {len(available_beds) - 5} more beds available\n"
        else:
            response += "❌ No beds currently available in this ward.\n"

        return response

    def _generate_emergency_response(self, result: Dict[str, Any]) -> str:
        """Generate response for emergency"""

        emergency_type = result.get("emergency_type", "general")
        response_id = result.get("response_id", "Unknown")

        response = f"🚨 **Emergency Response Activated**\n\n"
        response += f"🆔 **Response ID:** {response_id}\n"
        response += f"⚡ **Emergency Type:** {emergency_type.title()}\n"
        response += f"⏱️ **Estimated Response Time:** {result.get('estimated_response_time', '5-10 minutes')}\n\n"

        if result.get("mobilized_resources"):
            response += "**Actions Taken:**\n"
            for action in result["mobilized_resources"]:
                response += f"✅ {action}\n"

        response += "\n🏥 All relevant departments have been notified and are responding."

        return response

    def _extract_patient_data_from_message(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Extract patient data from user message and context"""

        # This would use NLP to extract patient information
        # For now, return basic structure
        return {
            "personal_info": {
                "name": params.get("patient_name", "Unknown Patient"),
                "age": params.get("age"),
                "gender": params.get("gender")
            },
            "medical_history": {
                "allergies": params.get("allergies", []),
                "current_medications": params.get("medications", []),
                "medical_conditions": params.get("conditions", [])
            },
            "insurance_info": params.get("insurance", {}),
            "emergency_contact": params.get("emergency_contact", {}),
            "admission_notes": params.get("user_message", "")
        }

    async def _notify_admission_departments(self, admission_result: Dict[str, Any]) -> None:
        """Notify relevant departments about patient admission"""

        departments = ["nursing", "housekeeping", "pharmacy", "laboratory"]

        for department in departments:
            await self.communication_bus.broadcast_event(
                "patient_admission",
                {
                    "patient_id": admission_result.get("patient_id"),
                    "bed_id": admission_result.get("assigned_bed"),
                    "department": department,
                    "timestamp": datetime.now().isoformat()
                },
                target_agents=[f"{department}_agent"]
            )

    def _update_conversation_context(
        self,
        user_id: str,
        user_message: str,
        response: str,
        result: CoordinationResult
    ) -> None:
        """Update conversation context for continuity"""

        if user_id not in self.conversation_contexts:
            self.conversation_contexts[user_id] = {
                "conversation_history": [],
                "active_requests": {},
                "user_preferences": {}
            }

        context = self.conversation_contexts[user_id]

        # Add to conversation history
        context["conversation_history"].append({
            "timestamp": datetime.now().isoformat(),
            "user_message": user_message,
            "response": response,
            "request_type": result.request_id,
            "success": result.success
        })

        # Keep only last 10 interactions
        if len(context["conversation_history"]) > 10:
            context["conversation_history"] = context["conversation_history"][-10:]

        # Track active requests
        if result.follow_up_actions:
            context["active_requests"][result.request_id] = {
                "follow_up_actions": result.follow_up_actions,
                "created_at": datetime.now().isoformat()
            }

    async def _optimize_hospital_operations(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize hospital operations (placeholder for complex optimization)"""

        # This would implement sophisticated optimization algorithms
        # For now, return a basic response
        return {
            "optimization_plan": {
                "bed_utilization": "Optimize bed allocation algorithms",
                "staff_scheduling": "Improve shift scheduling efficiency",
                "resource_allocation": "Better equipment distribution"
            },
            "expected_improvements": [
                "15% improvement in bed utilization",
                "20% reduction in patient wait times",
                "10% improvement in staff efficiency"
            ],
            "implementation_steps": [
                "Analyze current performance metrics",
                "Implement optimization algorithms",
                "Monitor and adjust parameters"
            ],
            "involved_agents": ["all_agents"],
            "actions_taken": ["Generated optimization plan"],
            "follow_up_actions": ["Begin implementation phase"]
        }
