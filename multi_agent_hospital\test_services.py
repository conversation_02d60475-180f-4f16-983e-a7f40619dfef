#!/usr/bin/env python3
"""
Test script to verify all external services are accessible
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_database():
    """Test PostgreSQL connection"""
    try:
        import asyncpg

        # Extract connection details from DATABASE_URL
        db_url = os.getenv('DATABASE_URL')
        print(f"Testing PostgreSQL connection: {db_url}")

        # Handle both postgresql:// and postgresql+asyncpg:// schemes
        if db_url.startswith('postgresql+asyncpg://'):
            db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')

        # Connect to database
        conn = await asyncpg.connect(db_url)

        # Test query
        result = await conn.fetchval('SELECT version()')
        print(f"✅ PostgreSQL connected successfully")
        print(f"   Database version: {result[:50]}...")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print(f"   Trying to connect to PostgreSQL...")

        # Try alternative connection method
        try:
            conn = await asyncpg.connect(
                host='localhost',
                port=5432,
                user='hospital_user',
                password='hospital_pass',
                database='hospital_db'
            )
            result = await conn.fetchval('SELECT version()')
            print(f"✅ PostgreSQL connected successfully (alternative method)")
            print(f"   Database version: {result[:50]}...")
            await conn.close()
            return True
        except Exception as e2:
            print(f"❌ Alternative PostgreSQL connection also failed: {e2}")
            return False

async def test_redis():
    """Test Redis connection"""
    try:
        import redis.asyncio as redis
        
        redis_url = os.getenv('REDIS_URL')
        print(f"Testing Redis connection: {redis_url}")
        
        # Connect to Redis
        r = redis.from_url(redis_url)
        
        # Test ping
        result = await r.ping()
        print(f"✅ Redis connected successfully: {result}")
        
        # Test set/get
        await r.set('test_key', 'test_value')
        value = await r.get('test_key')
        print(f"   Redis test operation successful: {value.decode()}")
        
        await r.close()
        return True
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

async def test_rabbitmq():
    """Test RabbitMQ connection"""
    try:
        import aio_pika
        
        rabbitmq_url = os.getenv('RABBITMQ_URL')
        print(f"Testing RabbitMQ connection: {rabbitmq_url}")
        
        # Connect to RabbitMQ
        connection = await aio_pika.connect_robust(rabbitmq_url)
        
        # Create channel
        channel = await connection.channel()
        print("✅ RabbitMQ connected successfully")
        
        # Test queue creation
        queue = await channel.declare_queue('test_queue', auto_delete=True)
        print(f"   RabbitMQ test queue created: {queue.name}")
        
        await connection.close()
        return True
        
    except Exception as e:
        print(f"❌ RabbitMQ connection failed: {e}")
        return False

def test_google_api():
    """Test Google Gemini API key"""
    try:
        api_key = os.getenv('GOOGLE_API_KEY')
        
        if not api_key or api_key == 'your_gemini_api_key_here':
            print("❌ Google API key not configured")
            return False
            
        if len(api_key) < 30:
            print("❌ Google API key appears to be invalid (too short)")
            return False
            
        print(f"✅ Google API key configured: {api_key[:10]}...{api_key[-10:]}")
        return True
        
    except Exception as e:
        print(f"❌ Google API key test failed: {e}")
        return False

def test_directories():
    """Test required directories exist"""
    try:
        required_dirs = ['data', 'data/chroma_db', 'logs']
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ Created directory: {dir_path}")
            else:
                print(f"✅ Directory exists: {dir_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Directory test failed: {e}")
        return False

async def main():
    """Run all service tests"""
    print("🏥 Hospital Multi-Agent System - Service Connectivity Test")
    print("=" * 60)
    
    # Test directories
    print("\n📁 Testing directories...")
    dirs_ok = test_directories()
    
    # Test Google API
    print("\n🔑 Testing Google API configuration...")
    api_ok = test_google_api()
    
    # Test database services
    print("\n🗄️ Testing database services...")
    db_ok = await test_database()
    
    print("\n🔴 Testing Redis...")
    redis_ok = await test_redis()
    
    print("\n🐰 Testing RabbitMQ...")
    rabbitmq_ok = await test_rabbitmq()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   Directories: {'✅' if dirs_ok else '❌'}")
    print(f"   Google API:  {'✅' if api_ok else '❌'}")
    print(f"   PostgreSQL:  {'✅' if db_ok else '❌'}")
    print(f"   Redis:       {'✅' if redis_ok else '❌'}")
    print(f"   RabbitMQ:    {'✅' if rabbitmq_ok else '❌'}")
    
    all_ok = all([dirs_ok, api_ok, db_ok, redis_ok, rabbitmq_ok])
    
    if all_ok:
        print("\n🎉 All services are ready! You can now start the Hospital System.")
        print("\nNext steps:")
        print("   1. Run: python start_system.py start --dev")
        print("   2. Open: http://localhost:8000/docs")
    else:
        print("\n❌ Some services failed. Please check the errors above.")
        print("\nTroubleshooting:")
        if not db_ok or not redis_ok or not rabbitmq_ok:
            print("   - Make sure Docker services are running: docker-compose up -d")
        if not api_ok:
            print("   - Check your Google API key in the .env file")

if __name__ == "__main__":
    asyncio.run(main())
