"""
Patient Repository

Data access layer for patient management operations.
For now, this uses in-memory data structures. In production,
this would connect to PostgreSQL database.
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import structlog

logger = structlog.get_logger(__name__)


class PatientStatus(Enum):
    """Patient status enumeration"""
    ADMITTED = "admitted"
    IN_TREATMENT = "in_treatment"
    RECOVERING = "recovering"
    READY_FOR_DISCHARGE = "ready_for_discharge"
    DISCHARGED = "discharged"
    TRANSFERRED = "transferred"


@dataclass
class Patient:
    """Patient data model"""
    id: str
    personal_info: Dict[str, Any]
    medical_history: Dict[str, Any]
    insurance_info: Dict[str, Any]
    emergency_contact: Dict[str, Any]
    admission_time: datetime
    primary_diagnosis: str
    secondary_diagnoses: List[str]
    current_bed_id: Optional[str]
    attending_physician: str
    status: PatientStatus
    care_plan_id: Optional[str]
    created_at: datetime
    updated_at: datetime


@dataclass
class CarePlan:
    """Care plan data model"""
    id: str
    patient_id: str
    treatment_goals: List[str]
    medications: List[Dict[str, Any]]
    procedures: List[Dict[str, Any]]
    monitoring_requirements: List[str]
    dietary_restrictions: List[str]
    activity_restrictions: List[str]
    estimated_length_of_stay: int
    discharge_criteria: List[str]
    created_at: datetime
    updated_at: datetime


class PatientRepository:
    """Repository for patient data operations"""
    
    def __init__(self):
        # In-memory data store (would be database in production)
        self.patients: Dict[str, Patient] = {}
        self.care_plans: Dict[str, CarePlan] = {}
        self._initialize_sample_data()
    
    def _initialize_sample_data(self):
        """Initialize with sample patient data"""
        sample_patients = [
            Patient(
                id="PAT_001",
                personal_info={
                    "name": "John Smith",
                    "age": 65,
                    "gender": "Male",
                    "date_of_birth": "1958-03-15"
                },
                medical_history={
                    "allergies": ["Penicillin"],
                    "chronic_conditions": ["Hypertension", "Diabetes Type 2"],
                    "previous_surgeries": ["Appendectomy (1985)"]
                },
                insurance_info={
                    "provider": "Medicare",
                    "policy_number": "123456789",
                    "group_number": "ABC123"
                },
                emergency_contact={
                    "name": "Mary Smith",
                    "relationship": "Spouse",
                    "phone": "555-0123"
                },
                admission_time=datetime.now() - timedelta(hours=6),
                primary_diagnosis="Acute Myocardial Infarction",
                secondary_diagnoses=["Hypertension", "Diabetes"],
                current_bed_id="ICU-002",
                attending_physician="Dr. Johnson",
                status=PatientStatus.IN_TREATMENT,
                care_plan_id="CP_001",
                created_at=datetime.now() - timedelta(hours=6),
                updated_at=datetime.now()
            ),
            Patient(
                id="PAT_002",
                personal_info={
                    "name": "Sarah Wilson",
                    "age": 42,
                    "gender": "Female",
                    "date_of_birth": "1981-07-22"
                },
                medical_history={
                    "allergies": [],
                    "chronic_conditions": [],
                    "previous_surgeries": []
                },
                insurance_info={
                    "provider": "Blue Cross",
                    "policy_number": "987654321",
                    "group_number": "XYZ789"
                },
                emergency_contact={
                    "name": "Mike Wilson",
                    "relationship": "Spouse",
                    "phone": "555-0456"
                },
                admission_time=datetime.now() - timedelta(hours=12),
                primary_diagnosis="Pneumonia",
                secondary_diagnoses=[],
                current_bed_id="MED-002",
                attending_physician="Dr. Brown",
                status=PatientStatus.RECOVERING,
                care_plan_id="CP_002",
                created_at=datetime.now() - timedelta(hours=12),
                updated_at=datetime.now()
            ),
            Patient(
                id="PAT_003",
                personal_info={
                    "name": "Robert Davis",
                    "age": 55,
                    "gender": "Male",
                    "date_of_birth": "1968-11-08"
                },
                medical_history={
                    "allergies": ["Latex"],
                    "chronic_conditions": ["Arthritis"],
                    "previous_surgeries": ["Knee Replacement (2020)"]
                },
                insurance_info={
                    "provider": "Aetna",
                    "policy_number": "456789123",
                    "group_number": "DEF456"
                },
                emergency_contact={
                    "name": "Linda Davis",
                    "relationship": "Spouse",
                    "phone": "555-0789"
                },
                admission_time=datetime.now() - timedelta(hours=24),
                primary_diagnosis="Post-operative monitoring",
                secondary_diagnoses=["Arthritis"],
                current_bed_id="SURG-002",
                attending_physician="Dr. Martinez",
                status=PatientStatus.READY_FOR_DISCHARGE,
                care_plan_id="CP_003",
                created_at=datetime.now() - timedelta(hours=24),
                updated_at=datetime.now()
            )
        ]
        
        for patient in sample_patients:
            self.patients[patient.id] = patient
        
        logger.info(f"Initialized {len(sample_patients)} sample patients")
    
    async def create_patient(
        self,
        personal_info: Dict[str, Any],
        medical_history: Dict[str, Any],
        insurance_info: Dict[str, Any],
        emergency_contact: Dict[str, Any]
    ) -> str:
        """Create a new patient record"""
        
        patient_id = f"PAT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        patient = Patient(
            id=patient_id,
            personal_info=personal_info,
            medical_history=medical_history,
            insurance_info=insurance_info,
            emergency_contact=emergency_contact,
            admission_time=datetime.now(),
            primary_diagnosis="To be determined",
            secondary_diagnoses=[],
            current_bed_id=None,
            attending_physician="TBD",
            status=PatientStatus.ADMITTED,
            care_plan_id=None,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        self.patients[patient_id] = patient
        
        logger.info(
            "Patient created",
            patient_id=patient_id,
            name=personal_info.get("name", "Unknown")
        )
        
        return patient_id
    
    async def get_patient(self, patient_id: str) -> Optional[Patient]:
        """Get patient by ID"""
        return self.patients.get(patient_id)
    
    async def update_patient(
        self,
        patient_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update patient information"""
        
        if patient_id not in self.patients:
            return False
        
        patient = self.patients[patient_id]
        
        # Update allowed fields
        if "primary_diagnosis" in updates:
            patient.primary_diagnosis = updates["primary_diagnosis"]
        
        if "secondary_diagnoses" in updates:
            patient.secondary_diagnoses = updates["secondary_diagnoses"]
        
        if "current_bed_id" in updates:
            patient.current_bed_id = updates["current_bed_id"]
        
        if "attending_physician" in updates:
            patient.attending_physician = updates["attending_physician"]
        
        if "status" in updates:
            patient.status = PatientStatus(updates["status"])
        
        if "care_plan_id" in updates:
            patient.care_plan_id = updates["care_plan_id"]
        
        patient.updated_at = datetime.now()
        
        logger.info("Patient updated", patient_id=patient_id, updates=list(updates.keys()))
        
        return True
    
    async def get_patients_in_ward(self, ward_type: str) -> List[Patient]:
        """Get all patients currently in a specific ward"""
        
        # This would require joining with bed data in a real database
        # For now, we'll use a simplified approach
        ward_patients = []
        
        for patient in self.patients.values():
            if patient.current_bed_id:
                # Extract ward from bed ID (simplified)
                if ward_type == "icu" and patient.current_bed_id.startswith("ICU"):
                    ward_patients.append(patient)
                elif ward_type == "general_medicine" and patient.current_bed_id.startswith("MED"):
                    ward_patients.append(patient)
                elif ward_type == "surgical" and patient.current_bed_id.startswith("SURG"):
                    ward_patients.append(patient)
                elif ward_type == "cardiology" and patient.current_bed_id.startswith("CARD"):
                    ward_patients.append(patient)
                elif ward_type == "pediatrics" and patient.current_bed_id.startswith("PED"):
                    ward_patients.append(patient)
        
        return ward_patients
    
    async def get_patients_by_status(self, status: PatientStatus) -> List[Patient]:
        """Get patients by status"""
        return [
            patient for patient in self.patients.values()
            if patient.status == status
        ]
    
    async def get_patients_by_physician(self, physician_name: str) -> List[Patient]:
        """Get patients assigned to a specific physician"""
        return [
            patient for patient in self.patients.values()
            if patient.attending_physician == physician_name
        ]
    
    async def search_patients(
        self,
        name: Optional[str] = None,
        patient_id: Optional[str] = None,
        bed_id: Optional[str] = None
    ) -> List[Patient]:
        """Search patients by various criteria"""
        
        results = []
        
        for patient in self.patients.values():
            match = True
            
            if name:
                patient_name = patient.personal_info.get("name", "").lower()
                if name.lower() not in patient_name:
                    match = False
            
            if patient_id:
                if patient_id.lower() not in patient.id.lower():
                    match = False
            
            if bed_id:
                if patient.current_bed_id != bed_id:
                    match = False
            
            if match:
                results.append(patient)
        
        return results
    
    async def discharge_patient(self, patient_id: str) -> bool:
        """Discharge a patient"""
        
        if patient_id not in self.patients:
            return False
        
        patient = self.patients[patient_id]
        patient.status = PatientStatus.DISCHARGED
        patient.current_bed_id = None
        patient.updated_at = datetime.now()
        
        logger.info("Patient discharged", patient_id=patient_id)
        
        return True
    
    async def transfer_patient(
        self,
        patient_id: str,
        new_bed_id: str,
        reason: str
    ) -> bool:
        """Transfer patient to a different bed"""
        
        if patient_id not in self.patients:
            return False
        
        patient = self.patients[patient_id]
        old_bed_id = patient.current_bed_id
        
        patient.current_bed_id = new_bed_id
        patient.status = PatientStatus.TRANSFERRED
        patient.updated_at = datetime.now()
        
        logger.info(
            "Patient transferred",
            patient_id=patient_id,
            from_bed=old_bed_id,
            to_bed=new_bed_id,
            reason=reason
        )
        
        return True
    
    async def get_patient_statistics(self) -> Dict[str, Any]:
        """Get patient statistics"""
        
        total_patients = len(self.patients)
        
        status_counts = {}
        for status in PatientStatus:
            status_counts[status.value] = sum(
                1 for patient in self.patients.values()
                if patient.status == status
            )
        
        # Calculate average length of stay for discharged patients
        discharged_patients = [
            patient for patient in self.patients.values()
            if patient.status == PatientStatus.DISCHARGED
        ]
        
        avg_length_of_stay = 0
        if discharged_patients:
            total_stay = sum(
                (patient.updated_at - patient.admission_time).days
                for patient in discharged_patients
            )
            avg_length_of_stay = total_stay / len(discharged_patients)
        
        return {
            "total_patients": total_patients,
            "status_distribution": status_counts,
            "average_length_of_stay_days": round(avg_length_of_stay, 1),
            "active_patients": sum(
                1 for patient in self.patients.values()
                if patient.status in [
                    PatientStatus.ADMITTED,
                    PatientStatus.IN_TREATMENT,
                    PatientStatus.RECOVERING
                ]
            )
        }
