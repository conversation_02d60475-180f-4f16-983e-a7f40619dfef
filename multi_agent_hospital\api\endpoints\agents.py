"""
Agents API Endpoints

REST API endpoints for agent management and monitoring.
"""

from typing import List, Dict, Any, Optional

import structlog
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

logger = structlog.get_logger(__name__)

router = APIRouter()


class AgentInfo(BaseModel):
    """Agent information model"""
    agent_id: str
    name: str
    description: str
    status: str
    capabilities: List[str]
    active_tasks: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    last_activity: Optional[str]


class AgentRegistryStats(BaseModel):
    """Agent registry statistics"""
    total_agents: int
    status_distribution: Dict[str, int]
    capabilities: int
    agent_types: int
    capability_coverage: Dict[str, int]


def get_agent_registry(request: Request):
    """Dependency to get agent registry"""
    if not hasattr(request.app.state, 'agent_registry'):
        raise HTTPException(status_code=503, detail="Agent registry not available")
    return request.app.state.agent_registry


@router.get("/", response_model=List[AgentInfo])
async def list_agents(
    agent_registry = Depends(get_agent_registry)
):
    """Get list of all registered agents"""
    try:
        agents = agent_registry.list_all_agents()
        
        agent_list = []
        for registration in agents:
            agent_info = AgentInfo(
                agent_id=registration.agent_id,
                name=registration.name,
                description=registration.description,
                status=registration.status.value,
                capabilities=[cap.name for cap in registration.capabilities],
                active_tasks=len(registration.agent_instance.active_tasks) if registration.agent_instance else 0,
                total_requests=registration.agent_instance.metrics.total_requests if registration.agent_instance else 0,
                successful_requests=registration.agent_instance.metrics.successful_requests if registration.agent_instance else 0,
                failed_requests=registration.agent_instance.metrics.failed_requests if registration.agent_instance else 0,
                average_response_time=registration.agent_instance.metrics.average_response_time if registration.agent_instance else 0,
                last_activity=registration.last_heartbeat.isoformat()
            )
            agent_list.append(agent_info)
        
        return agent_list
        
    except Exception as e:
        logger.error("Error listing agents", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}")


@router.get("/stats", response_model=AgentRegistryStats)
async def get_agent_stats(
    agent_registry = Depends(get_agent_registry)
):
    """Get agent registry statistics"""
    try:
        stats = agent_registry.get_registry_stats()
        
        return AgentRegistryStats(
            total_agents=stats["total_agents"],
            status_distribution=stats["status_distribution"],
            capabilities=stats["capabilities"],
            agent_types=stats["agent_types"],
            capability_coverage=stats["capability_coverage"]
        )
        
    except Exception as e:
        logger.error("Error getting agent stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get agent stats: {str(e)}")


@router.get("/{agent_id}")
async def get_agent(
    agent_id: str,
    agent_registry = Depends(get_agent_registry)
):
    """Get specific agent information"""
    try:
        registration = agent_registry.get_agent(agent_id)
        
        if not registration:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return {
            "agent_id": registration.agent_id,
            "name": registration.name,
            "description": registration.description,
            "status": registration.status.value,
            "capabilities": [
                {
                    "name": cap.name,
                    "description": cap.description,
                    "estimated_duration_seconds": cap.estimated_duration_seconds
                }
                for cap in registration.capabilities
            ],
            "registered_at": registration.registered_at.isoformat(),
            "last_heartbeat": registration.last_heartbeat.isoformat(),
            "metrics": registration.agent_instance.metrics.dict() if registration.agent_instance else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting agent", agent_id=agent_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get agent: {str(e)}")


@router.get("/capabilities/{capability_name}")
async def get_agents_by_capability(
    capability_name: str,
    agent_registry = Depends(get_agent_registry)
):
    """Get agents that have a specific capability"""
    try:
        agents = agent_registry.find_agents_by_capability(capability_name)
        
        if not agents:
            return {
                "capability": capability_name,
                "agents": [],
                "count": 0
            }
        
        agent_list = []
        for registration in agents:
            agent_list.append({
                "agent_id": registration.agent_id,
                "name": registration.name,
                "status": registration.status.value,
                "load": agent_registry.get_agent_load(registration.agent_id)
            })
        
        return {
            "capability": capability_name,
            "agents": agent_list,
            "count": len(agent_list)
        }
        
    except Exception as e:
        logger.error("Error getting agents by capability", capability=capability_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get agents by capability: {str(e)}")


@router.get("/types/{agent_type}")
async def get_agents_by_type(
    agent_type: str,
    agent_registry = Depends(get_agent_registry)
):
    """Get agents of a specific type"""
    try:
        agents = agent_registry.find_agents_by_type(agent_type)
        
        if not agents:
            return {
                "agent_type": agent_type,
                "agents": [],
                "count": 0
            }
        
        agent_list = []
        for registration in agents:
            agent_list.append({
                "agent_id": registration.agent_id,
                "name": registration.name,
                "status": registration.status.value,
                "active_tasks": len(registration.agent_instance.active_tasks) if registration.agent_instance else 0
            })
        
        return {
            "agent_type": agent_type,
            "agents": agent_list,
            "count": len(agent_list)
        }
        
    except Exception as e:
        logger.error("Error getting agents by type", agent_type=agent_type, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get agents by type: {str(e)}")


@router.post("/{agent_id}/heartbeat")
async def agent_heartbeat(
    agent_id: str,
    agent_registry = Depends(get_agent_registry)
):
    """Send heartbeat to specific agent"""
    try:
        registration = agent_registry.get_agent(agent_id)
        
        if not registration:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        # Get agent status through communication bus
        communication_bus = agent_registry.communication_bus
        status = await communication_bus.get_agent_status(agent_id)
        
        return {
            "agent_id": agent_id,
            "heartbeat_sent": True,
            "status": status,
            "timestamp": registration.last_heartbeat.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error sending heartbeat", agent_id=agent_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to send heartbeat: {str(e)}")


@router.get("/capabilities/available")
async def get_available_capabilities(
    agent_registry = Depends(get_agent_registry)
):
    """Get all available capabilities across all agents"""
    try:
        capabilities = agent_registry.get_available_capabilities()
        
        capability_list = []
        for capability_name, agent_ids in capabilities.items():
            capability_list.append({
                "name": capability_name,
                "agent_count": len(agent_ids),
                "agents": agent_ids
            })
        
        return {
            "capabilities": capability_list,
            "total_capabilities": len(capability_list)
        }
        
    except Exception as e:
        logger.error("Error getting available capabilities", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get capabilities: {str(e)}")


@router.get("/load-balancing/{capability_name}")
async def get_least_loaded_agent(
    capability_name: str,
    agent_registry = Depends(get_agent_registry)
):
    """Get the least loaded agent for a specific capability"""
    try:
        agent_id = agent_registry.find_least_loaded_agent(capability_name)
        
        if not agent_id:
            return {
                "capability": capability_name,
                "least_loaded_agent": None,
                "message": "No available agents for this capability"
            }
        
        registration = agent_registry.get_agent(agent_id)
        load = agent_registry.get_agent_load(agent_id)
        
        return {
            "capability": capability_name,
            "least_loaded_agent": {
                "agent_id": agent_id,
                "name": registration.name,
                "load": load,
                "status": registration.status.value
            }
        }
        
    except Exception as e:
        logger.error("Error finding least loaded agent", capability=capability_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to find least loaded agent: {str(e)}")
